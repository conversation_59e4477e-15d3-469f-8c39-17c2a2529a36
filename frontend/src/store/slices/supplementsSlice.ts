/**
 * Supplements Redux Slice
 * 
 * Manages supplement catalog, user supplements, and intake tracking state
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface Supplement {
  id: number;
  name: string;
  brand?: string;
  category?: string;
  description?: string;
  default_dosage?: string;
  dosage_unit?: string;
  evidence_level?: string;
  created_at: string;
}

export interface UserSupplement {
  id: number;
  supplement_id: number;
  custom_name?: string;
  dosage: string;
  frequency: string;
  time_of_day?: string;
  notes?: string;
  user_id: number;
  is_active: boolean;
  start_date: string;
  supplement: Supplement;
}

export interface IntakeLog {
  id: number;
  user_supplement_id: number;
  taken_at: string;
  dosage_taken?: string;
  notes?: string;
  mood_before?: number;
  mood_after?: number;
  user_id: number;
  created_at: string;
}

export interface SupplementsState {
  // Catalog
  catalog: Supplement[];
  catalogLoading: boolean;
  catalogError: string | null;
  
  // User supplements
  userSupplements: UserSupplement[];
  userSupplementsLoading: boolean;
  userSupplementsError: string | null;
  
  // Intake logs
  intakeLogs: IntakeLog[];
  intakeLogsLoading: boolean;
  intakeLogsError: string | null;
  
  // UI state
  selectedSupplement: Supplement | null;
  searchQuery: string;
  selectedCategory: string | null;
  
  // Stats
  stats: {
    active_supplements: number;
    total_intakes: number;
    weekly_intakes: number;
    adherence_rate: number;
  } | null;
  statsLoading: boolean;
}

const initialState: SupplementsState = {
  catalog: [],
  catalogLoading: false,
  catalogError: null,
  
  userSupplements: [],
  userSupplementsLoading: false,
  userSupplementsError: null,
  
  intakeLogs: [],
  intakeLogsLoading: false,
  intakeLogsError: null,
  
  selectedSupplement: null,
  searchQuery: '',
  selectedCategory: null,
  
  stats: null,
  statsLoading: false,
};

// API base URL
const API_BASE = 'http://api.pills.localhost/api/v1';

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Async thunks
export const fetchSupplementCatalog = createAsyncThunk(
  'supplements/fetchCatalog',
  async (params: { search?: string; category?: string; skip?: number; limit?: number } = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params.search) queryParams.append('search', params.search);
      if (params.category) queryParams.append('category', params.category);
      if (params.skip) queryParams.append('skip', params.skip.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());

      const response = await fetch(`${API_BASE}/supplements/catalog?${queryParams}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch supplement catalog');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchUserSupplements = createAsyncThunk(
  'supplements/fetchUserSupplements',
  async (activeOnly: boolean = true, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/my-supplements?active_only=${activeOnly}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user supplements');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const addSupplementToSchedule = createAsyncThunk(
  'supplements/addToSchedule',
  async (supplementData: {
    supplement_id: number;
    dosage: string;
    frequency: string;
    time_of_day?: string;
    notes?: string;
    custom_name?: string;
  }, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/my-supplements`, {
        method: 'POST',
        headers,
        body: JSON.stringify(supplementData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to add supplement to schedule');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateSupplementSchedule = createAsyncThunk(
  'supplements/updateSchedule',
  async ({ id, data }: { id: number; data: Partial<UserSupplement> }, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/my-supplements/${id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to update supplement schedule');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const removeSupplementFromSchedule = createAsyncThunk(
  'supplements/removeFromSchedule',
  async (id: number, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/my-supplements/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to remove supplement from schedule');
      }

      return id;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const logIntake = createAsyncThunk(
  'supplements/logIntake',
  async (intakeData: {
    user_supplement_id: number;
    taken_at?: string;
    dosage_taken?: string;
    notes?: string;
    mood_before?: number;
    mood_after?: number;
  }, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/intake`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...intakeData,
          taken_at: intakeData.taken_at || new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to log intake');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchIntakeHistory = createAsyncThunk(
  'supplements/fetchIntakeHistory',
  async (params: {
    skip?: number;
    limit?: number;
    supplement_id?: number;
    start_date?: string;
    end_date?: string;
  } = {}, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const queryParams = new URLSearchParams();
      if (params.skip) queryParams.append('skip', params.skip.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.supplement_id) queryParams.append('supplement_id', params.supplement_id.toString());
      if (params.start_date) queryParams.append('start_date', params.start_date);
      if (params.end_date) queryParams.append('end_date', params.end_date);

      const response = await fetch(`${API_BASE}/supplements/intake/history?${queryParams}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to fetch intake history');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchSupplementStats = createAsyncThunk(
  'supplements/fetchStats',
  async (_, { getState, rejectWithValue }) => {
    try {
      const headers = getAuthHeaders(getState);
      const response = await fetch(`${API_BASE}/supplements/stats`, {
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to fetch supplement stats');
      }

      return await response.json();
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const supplementsSlice = createSlice({
  name: 'supplements',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedCategory: (state, action: PayloadAction<string | null>) => {
      state.selectedCategory = action.payload;
    },
    setSelectedSupplement: (state, action: PayloadAction<Supplement | null>) => {
      state.selectedSupplement = action.payload;
    },
    clearErrors: (state) => {
      state.catalogError = null;
      state.userSupplementsError = null;
      state.intakeLogsError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch catalog
      .addCase(fetchSupplementCatalog.pending, (state) => {
        state.catalogLoading = true;
        state.catalogError = null;
      })
      .addCase(fetchSupplementCatalog.fulfilled, (state, action) => {
        state.catalogLoading = false;
        state.catalog = action.payload;
      })
      .addCase(fetchSupplementCatalog.rejected, (state, action) => {
        state.catalogLoading = false;
        state.catalogError = action.payload as string;
      })
      
      // Fetch user supplements
      .addCase(fetchUserSupplements.pending, (state) => {
        state.userSupplementsLoading = true;
        state.userSupplementsError = null;
      })
      .addCase(fetchUserSupplements.fulfilled, (state, action) => {
        state.userSupplementsLoading = false;
        state.userSupplements = action.payload;
      })
      .addCase(fetchUserSupplements.rejected, (state, action) => {
        state.userSupplementsLoading = false;
        state.userSupplementsError = action.payload as string;
      })
      
      // Add supplement to schedule
      .addCase(addSupplementToSchedule.fulfilled, (state, action) => {
        state.userSupplements.push(action.payload);
      })
      .addCase(addSupplementToSchedule.rejected, (state, action) => {
        state.userSupplementsError = action.payload as string;
      })
      
      // Update supplement schedule
      .addCase(updateSupplementSchedule.fulfilled, (state, action) => {
        const index = state.userSupplements.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.userSupplements[index] = action.payload;
        }
      })
      
      // Remove supplement from schedule
      .addCase(removeSupplementFromSchedule.fulfilled, (state, action) => {
        state.userSupplements = state.userSupplements.filter(s => s.id !== action.payload);
      })
      
      // Log intake
      .addCase(logIntake.fulfilled, (state, action) => {
        state.intakeLogs.unshift(action.payload);
      })
      .addCase(logIntake.rejected, (state, action) => {
        state.intakeLogsError = action.payload as string;
      })
      
      // Fetch intake history
      .addCase(fetchIntakeHistory.pending, (state) => {
        state.intakeLogsLoading = true;
        state.intakeLogsError = null;
      })
      .addCase(fetchIntakeHistory.fulfilled, (state, action) => {
        state.intakeLogsLoading = false;
        state.intakeLogs = action.payload;
      })
      .addCase(fetchIntakeHistory.rejected, (state, action) => {
        state.intakeLogsLoading = false;
        state.intakeLogsError = action.payload as string;
      })
      
      // Fetch stats
      .addCase(fetchSupplementStats.pending, (state) => {
        state.statsLoading = true;
      })
      .addCase(fetchSupplementStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload;
      })
      .addCase(fetchSupplementStats.rejected, (state) => {
        state.statsLoading = false;
      });
  },
});

export const { setSearchQuery, setSelectedCategory, setSelectedSupplement, clearErrors } = supplementsSlice.actions;

// Selectors
export const selectCatalog = (state: { supplements: SupplementsState }) => state.supplements.catalog;
export const selectCatalogLoading = (state: { supplements: SupplementsState }) => state.supplements.catalogLoading;
export const selectUserSupplements = (state: { supplements: SupplementsState }) => state.supplements.userSupplements;
export const selectUserSupplementsLoading = (state: { supplements: SupplementsState }) => state.supplements.userSupplementsLoading;
export const selectIntakeLogs = (state: { supplements: SupplementsState }) => state.supplements.intakeLogs;
export const selectSelectedSupplement = (state: { supplements: SupplementsState }) => state.supplements.selectedSupplement;
export const selectSearchQuery = (state: { supplements: SupplementsState }) => state.supplements.searchQuery;
export const selectSelectedCategory = (state: { supplements: SupplementsState }) => state.supplements.selectedCategory;
export const selectSupplementStats = (state: { supplements: SupplementsState }) => state.supplements.stats;

export default supplementsSlice.reducer;
