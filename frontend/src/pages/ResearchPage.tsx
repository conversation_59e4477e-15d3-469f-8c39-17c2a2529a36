/**
 * Research Platform Page
 * 
 * Main page for research study participation and management
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Microscope, 
  Search, 
  Users, 
  TrendingUp, 
  Award,
  FileText,
  Calendar,
  Target,
  BookOpen,
  Lightbulb
} from 'lucide-react';
import StudyBrowser from '@/components/research/StudyBrowser';
import StudyParticipation from '@/components/research/StudyParticipation';
import { useToast } from '@/hooks/use-toast';

const ResearchPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('browse');
  const { toast } = useToast();

  const handleJoinStudy = async (studyId: number) => {
    try {
      const response = await fetch(`http://api.pills.localhost/api/v1/research/studies/${studyId}/join`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast({
          title: "Successfully joined study!",
          description: "You can now track your participation in the My Studies tab.",
        });
        setActiveTab('participation');
      } else {
        const error = await response.json();
        toast({
          title: "Failed to join study",
          description: error.detail || "Please try again later.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to join study",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleViewStudyDetails = (study: any) => {
    // Could open a modal or navigate to detail page
    console.log('View study details:', study);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <Microscope className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Research Platform
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Participate in cutting-edge supplement research studies
            </p>
          </div>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Search className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Available</p>
                  <p className="text-xl font-bold">12</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                  <p className="text-xl font-bold">2</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <Award className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                  <p className="text-xl font-bold">5</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Points</p>
                  <p className="text-xl font-bold">500</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            <span className="hidden sm:inline">Browse Studies</span>
          </TabsTrigger>
          <TabsTrigger value="participation" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <span className="hidden sm:inline">My Studies</span>
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            <span className="hidden sm:inline">Data Collection</span>
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Lightbulb className="w-4 h-4" />
            <span className="hidden sm:inline">Insights</span>
          </TabsTrigger>
        </TabsList>

        {/* Browse Studies Tab */}
        <TabsContent value="browse" className="space-y-6">
          <StudyBrowser 
            onJoinStudy={handleJoinStudy}
            onViewDetails={handleViewStudyDetails}
          />
        </TabsContent>

        {/* My Studies Tab */}
        <TabsContent value="participation" className="space-y-6">
          <StudyParticipation />
        </TabsContent>

        {/* Data Collection Tab */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Data Collection Forms
              </CardTitle>
              <CardDescription>
                Complete data collection forms for your active studies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Data Collection Coming Soon
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Interactive forms for study data collection will be available here.
                </p>
                <div className="flex gap-2 justify-center">
                  <Badge variant="outline">Daily Surveys</Badge>
                  <Badge variant="outline">Mood Tracking</Badge>
                  <Badge variant="outline">Side Effect Reports</Badge>
                  <Badge variant="outline">Biomarker Data</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Research Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                Research Insights
              </CardTitle>
              <CardDescription>
                Discover insights from completed studies and research findings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Featured Insights */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Vitamin D3 and Sleep Quality</CardTitle>
                      <CardDescription>Recent study findings</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        Participants taking 2000 IU of Vitamin D3 daily showed a 23% improvement in sleep quality scores over 12 weeks.
                      </p>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline">156 participants</Badge>
                        <Button size="sm" variant="outline">
                          <BookOpen className="w-3 h-3 mr-1" />
                          Read More
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-green-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Omega-3 and Cognitive Function</CardTitle>
                      <CardDescription>Ongoing research</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        Preliminary data suggests improved memory test scores in participants taking high-dose omega-3 supplements.
                      </p>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline">89 participants</Badge>
                        <Button size="sm" variant="outline">
                          <Target className="w-3 h-3 mr-1" />
                          Join Study
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-purple-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Magnesium and Exercise Recovery</CardTitle>
                      <CardDescription>Completed study</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        Athletes supplementing with magnesium showed 18% faster recovery times and reduced muscle soreness.
                      </p>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline">45 participants</Badge>
                        <Button size="sm" variant="outline">
                          <BookOpen className="w-3 h-3 mr-1" />
                          View Results
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-orange-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Probiotic Gut Health Study</CardTitle>
                      <CardDescription>Recruiting now</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                        Investigating the effects of multi-strain probiotics on digestive health and immune function.
                      </p>
                      <div className="flex justify-between items-center">
                        <Badge variant="outline">78/120 participants</Badge>
                        <Button size="sm" variant="outline">
                          <Users className="w-3 h-3 mr-1" />
                          Join Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Research Impact */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Research Impact
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                          1,247
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Total Participants
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                          23
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Completed Studies
                        </p>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                          8
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Published Papers
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ResearchPage;
