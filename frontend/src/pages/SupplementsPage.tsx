/**
 * Supplements Page
 * 
 * Main page for supplement management including catalog browsing and personal schedule
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Pill, 
  Search, 
  Calendar, 
  TrendingUp, 
  BookOpen,
  Plus,
  Clock,
  Target
} from 'lucide-react';
import SupplementCatalog from '@/components/supplements/SupplementCatalog';
import UserSupplementsDashboard from '@/components/supplements/UserSupplementsDashboard';
import IntakeTracker from '@/components/intake/IntakeTracker';
import IntakeAnalytics from '@/components/analytics/IntakeAnalytics';

const SupplementsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedSupplement, setSelectedSupplement] = useState<any>(null);

  const handleSelectSupplement = (supplement: any) => {
    setSelectedSupplement(supplement);
    // Could open a modal or navigate to detail page
    console.log('Selected supplement:', supplement);
  };

  const handleAddSupplement = () => {
    setActiveTab('catalog');
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <Pill className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Supplements
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage your supplement routine and discover new options
            </p>
          </div>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                  <p className="text-xl font-bold">5</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Today</p>
                  <p className="text-xl font-bold">3/5</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Streak</p>
                  <p className="text-xl font-bold">7 days</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <BookOpen className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Research</p>
                  <p className="text-xl font-bold">12</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span className="hidden sm:inline">My Routine</span>
          </TabsTrigger>
          <TabsTrigger value="tracking" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span className="hidden sm:inline">Track</span>
          </TabsTrigger>
          <TabsTrigger value="catalog" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            <span className="hidden sm:inline">Catalog</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="research" className="flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            <span className="hidden sm:inline">Research</span>
          </TabsTrigger>
        </TabsList>

        {/* My Routine Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <UserSupplementsDashboard onAddSupplement={handleAddSupplement} />
        </TabsContent>

        {/* Intake Tracking Tab */}
        <TabsContent value="tracking" className="space-y-6">
          <IntakeTracker />
        </TabsContent>

        {/* Catalog Tab */}
        <TabsContent value="catalog" className="space-y-6">
          <SupplementCatalog
            onSelectSupplement={handleSelectSupplement}
            showAddButton={true}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <IntakeAnalytics />
        </TabsContent>

        {/* Research Tab */}
        <TabsContent value="research" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Research Studies
              </CardTitle>
              <CardDescription>
                Participate in supplement research and view study results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full flex items-center justify-center">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Research Platform Available
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Join research studies and contribute to supplement science.
                </p>
                <div className="flex gap-2 justify-center mb-4">
                  <Badge variant="outline">Clinical Studies</Badge>
                  <Badge variant="outline">Community Research</Badge>
                  <Badge variant="outline">Evidence Reviews</Badge>
                </div>
                <Button
                  onClick={() => window.location.href = '/research'}
                  className="flex items-center gap-2"
                >
                  <BookOpen className="w-4 h-4" />
                  Open Research Platform
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Floating Action Button for Mobile */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <Button
          size="lg"
          className="rounded-full w-14 h-14 shadow-lg"
          onClick={handleAddSupplement}
        >
          <Plus className="w-6 h-6" />
        </Button>
      </div>
    </div>
  );
};

export default SupplementsPage;
