/**
 * Landing Page Component
 * 
 * Simple landing page showcasing the Supplement Tracker with MVP components
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Activity, Database, Settings, ArrowRight } from 'lucide-react';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Activity className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              Supplement Tracker
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Evidence-based supplement research and tracking platform with modern UI components
          </p>
          <div className="flex items-center justify-center mt-4 space-x-2">
            <Badge variant="secondary">Port-Free Access</Badge>
            <Badge variant="outline">Modern Components</Badge>
            <Badge variant="default">MVP Template</Badge>
          </div>
        </div>

        {/* Quick Access */}
        <div className="text-center mb-8">
          <Button asChild size="lg" className="mr-4">
            <a href="/login">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <a href="/demo.html">
              View Demo
            </a>
          </Button>
        </div>

        {/* Service Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <Activity className="h-8 w-8 text-green-600 mr-3" />
              <h3 className="text-xl font-semibold">Interactive Demo</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Explore the full-featured demo with modern UI components and interactive features.
            </p>
            <Button asChild className="w-full">
              <a href="/demo.html">
                Launch Demo
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <Database className="h-8 w-8 text-blue-600 mr-3" />
              <h3 className="text-xl font-semibold">API Documentation</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Comprehensive API documentation with interactive testing capabilities.
            </p>
            <Button variant="secondary" asChild className="w-full">
              <a href="https://api.pills.localhost/docs" target="_blank" rel="noopener noreferrer">
                View API Docs
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </Card>

          <Card className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <Settings className="h-8 w-8 text-purple-600 mr-3" />
              <h3 className="text-xl font-semibold">Service Dashboard</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Monitor service health, routing, and performance through Traefik.
            </p>
            <Button variant="outline" asChild className="w-full">
              <a href="http://traefik.pills.localhost:9081/" target="_blank" rel="noopener noreferrer">
                Open Dashboard
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </Card>
        </div>

        {/* Status Section */}
        <Card className="p-8 text-center bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
          <div className="flex items-center justify-center mb-4">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3"></div>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              All Services Online
            </h3>
          </div>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Frontend</p>
              <p className="text-green-600">https://app.pills.localhost/</p>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">API</p>
              <p className="text-green-600">https://api.pills.localhost/</p>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Dashboard</p>
              <p className="text-green-600">http://traefik.pills.localhost:9081/</p>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center mt-12 text-gray-500 dark:text-gray-400">
          <p>Powered by React, FastAPI, Traefik, and Docker • MVP Template Components</p>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
