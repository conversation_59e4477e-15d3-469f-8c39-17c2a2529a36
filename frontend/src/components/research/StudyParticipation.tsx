/**
 * Study Participation Component
 * 
 * Manage active study participations and track progress
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  Award,
  FileText,
  Users,
  Target,
  Star
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface StudyParticipation {
  id: number;
  study_id: number;
  user_id: number;
  joined_at: string;
  status: string;
  progress_percentage: number;
  next_task?: string;
  next_task_due?: string;
  completion_date?: string;
  rating?: number;
  feedback?: string;
  study: {
    id: number;
    title: string;
    description: string;
    category: string;
    duration_weeks: number;
    researcher_name?: string;
    institution?: string;
    compensation?: string;
  };
}

interface DashboardStats {
  active_participations: number;
  completed_studies: number;
  available_studies: number;
  research_points: number;
}

const StudyParticipation: React.FC = () => {
  const [participations, setParticipations] = useState<StudyParticipation[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'active' | 'completed'>('active');
  const { toast } = useToast();

  useEffect(() => {
    fetchParticipations();
    fetchStats();
  }, []);

  const fetchParticipations = async () => {
    try {
      const response = await fetch('http://api.pills.localhost/api/v1/research/my-participations', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setParticipations(data);
      }
    } catch (error) {
      toast({
        title: "Failed to load participations",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('http://api.pills.localhost/api/v1/research/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleUpdateProgress = async (participationId: number, progress: number) => {
    try {
      const response = await fetch(
        `http://api.pills.localhost/api/v1/research/participations/${participationId}/progress`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            progress_percentage: progress,
          }),
        }
      );
      
      if (response.ok) {
        fetchParticipations();
        toast({
          title: "Progress updated",
          description: "Your study progress has been updated.",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to update progress",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleRateStudy = async (studyId: number, rating: number, feedback?: string) => {
    try {
      const response = await fetch(
        `http://api.pills.localhost/api/v1/research/studies/${studyId}/rate`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ rating, feedback }),
        }
      );
      
      if (response.ok) {
        fetchParticipations();
        toast({
          title: "Rating submitted",
          description: "Thank you for your feedback!",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to submit rating",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'withdrawn': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Vitamins': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'Fatty Acids': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
      'Minerals': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'Probiotics': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  const activeParticipations = participations.filter(p => p.status === 'active');
  const completedParticipations = participations.filter(p => p.status === 'completed');

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Research Participation
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Track your progress in ongoing studies
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Studies
                  </p>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {stats.active_participations}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Completed
                  </p>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {stats.completed_studies}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Available
                  </p>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {stats.available_studies}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <Award className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Research Points
                  </p>
                  <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {stats.research_points}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex gap-2">
        <Button
          variant={selectedTab === 'active' ? 'default' : 'outline'}
          onClick={() => setSelectedTab('active')}
          className="flex items-center gap-2"
        >
          <TrendingUp className="w-4 h-4" />
          Active Studies ({activeParticipations.length})
        </Button>
        <Button
          variant={selectedTab === 'completed' ? 'default' : 'outline'}
          onClick={() => setSelectedTab('completed')}
          className="flex items-center gap-2"
        >
          <CheckCircle className="w-4 h-4" />
          Completed ({completedParticipations.length})
        </Button>
      </div>

      {/* Active Studies */}
      {selectedTab === 'active' && (
        <div className="space-y-6">
          {activeParticipations.length > 0 ? (
            activeParticipations.map((participation) => (
              <Card key={participation.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{participation.study.title}</CardTitle>
                      <CardDescription className="flex items-center gap-2 mt-1">
                        {participation.study.institution && (
                          <>
                            <Users className="w-3 h-3" />
                            {participation.study.institution}
                          </>
                        )}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusColor(participation.status)}>
                      {participation.status}
                    </Badge>
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge className={getCategoryColor(participation.study.category)}>
                      {participation.study.category}
                    </Badge>
                    <Badge variant="outline">
                      <Clock className="w-3 h-3 mr-1" />
                      {participation.study.duration_weeks} weeks
                    </Badge>
                    {participation.study.compensation && (
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        {participation.study.compensation}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {participation.study.description}
                  </p>
                  
                  {/* Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">Progress</span>
                      <span>{Math.round(participation.progress_percentage)}%</span>
                    </div>
                    <Progress value={participation.progress_percentage} className="h-2" />
                  </div>
                  
                  {/* Next Task */}
                  {participation.next_task && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-start gap-2">
                        <AlertCircle className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            Next Task
                          </p>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {participation.next_task}
                          </p>
                          {participation.next_task_due && (
                            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                              Due: {format(new Date(participation.next_task_due), 'MMM d, yyyy')}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button size="sm" className="flex items-center gap-1">
                      <FileText className="w-3 h-3" />
                      View Forms
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleUpdateProgress(participation.id, participation.progress_percentage + 10)}
                    >
                      Update Progress
                    </Button>
                  </div>
                  
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Joined {format(new Date(participation.joined_at), 'MMM d, yyyy')}
                    {participation.study.researcher_name && (
                      <> • Led by {participation.study.researcher_name}</>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <Users className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No active studies
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Browse available studies to start participating in research.
                </p>
                <Button variant="outline">
                  Browse Studies
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Completed Studies */}
      {selectedTab === 'completed' && (
        <div className="space-y-6">
          {completedParticipations.length > 0 ? (
            completedParticipations.map((participation) => (
              <Card key={participation.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{participation.study.title}</CardTitle>
                      <CardDescription>
                        Completed {participation.completion_date && format(new Date(participation.completion_date), 'MMM d, yyyy')}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {participation.rating && (
                        <div className="flex items-center gap-1 text-sm">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span>{participation.rating}</span>
                        </div>
                      )}
                      <Badge className={getStatusColor(participation.status)}>
                        {participation.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Duration: {participation.study.duration_weeks} weeks
                    </div>
                    {!participation.rating && (
                      <Button 
                        size="sm"
                        onClick={() => handleRateStudy(participation.study_id, 5)}
                      >
                        Rate Study
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No completed studies
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Complete your first study to see it here.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default StudyParticipation;
