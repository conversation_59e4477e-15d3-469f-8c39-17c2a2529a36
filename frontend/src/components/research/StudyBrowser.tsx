/**
 * Study Browser Component
 * 
 * Browse and discover available research studies
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Users, 
  Clock, 
  Star, 
  Calendar,
  Award,
  Filter,
  MapPin,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface ResearchStudy {
  id: number;
  title: string;
  description: string;
  category: string;
  duration_weeks: number;
  max_participants: number;
  current_participants: number;
  difficulty: string;
  compensation?: string;
  requirements?: string;
  researcher_name?: string;
  institution?: string;
  status: string;
  rating: number;
  rating_count: number;
  start_date?: string;
  estimated_completion?: string;
  created_at: string;
}

interface StudyBrowserProps {
  onJoinStudy?: (studyId: number) => void;
  onViewDetails?: (study: ResearchStudy) => void;
}

const StudyBrowser: React.FC<StudyBrowserProps> = ({
  onJoinStudy,
  onViewDetails
}) => {
  const [studies, setStudies] = useState<ResearchStudy[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const { toast } = useToast();

  const categories = [
    'All',
    'Vitamins',
    'Fatty Acids',
    'Minerals',
    'Probiotics',
    'Herbs',
    'Cognitive',
    'Performance'
  ];

  const difficulties = [
    'All',
    'beginner',
    'intermediate',
    'advanced'
  ];

  useEffect(() => {
    fetchStudies();
  }, [searchTerm, selectedCategory, selectedDifficulty]);

  const fetchStudies = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory && selectedCategory !== 'All') params.append('category', selectedCategory);
      if (selectedDifficulty && selectedDifficulty !== 'All') params.append('difficulty', selectedDifficulty);
      params.append('status', 'recruiting');

      const response = await fetch(`http://api.pills.localhost/api/v1/research/studies?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setStudies(data);
      } else {
        throw new Error('Failed to fetch studies');
      }
    } catch (error) {
      toast({
        title: "Failed to load studies",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Vitamins': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'Fatty Acids': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
      'Minerals': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'Probiotics': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'Herbs': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300',
      'Cognitive': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
      'Performance': 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  const getAvailabilityPercentage = (current: number, max: number) => {
    return max > 0 ? ((max - current) / max) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Research Studies
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Participate in cutting-edge supplement research
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <TrendingUp className="w-3 h-3" />
          {studies.length} studies available
        </Badge>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search studies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2 overflow-x-auto pb-2">
          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category === 'All' ? '' : category)}
                className="whitespace-nowrap"
              >
                {category === 'All' ? (
                  <>
                    <Filter className="w-3 h-3 mr-1" />
                    All
                  </>
                ) : (
                  category
                )}
              </Button>
            ))}
          </div>
          
          <div className="flex gap-2 border-l pl-2">
            {difficulties.map((difficulty) => (
              <Button
                key={difficulty}
                variant={selectedDifficulty === difficulty ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedDifficulty(difficulty === 'All' ? '' : difficulty)}
                className="whitespace-nowrap"
              >
                {difficulty === 'All' ? 'All Levels' : difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Studies Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {studies.map((study) => {
          const availabilityPercentage = getAvailabilityPercentage(study.current_participants, study.max_participants);
          const spotsLeft = study.max_participants - study.current_participants;
          
          return (
            <Card 
              key={study.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => onViewDetails?.(study)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2">{study.title}</CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      {study.institution && (
                        <>
                          <MapPin className="w-3 h-3" />
                          {study.institution}
                        </>
                      )}
                    </CardDescription>
                  </div>
                  {study.rating > 0 && (
                    <div className="flex items-center gap-1 text-sm">
                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      <span>{study.rating.toFixed(1)}</span>
                      <span className="text-gray-500">({study.rating_count})</span>
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2 flex-wrap">
                  <Badge className={getCategoryColor(study.category)}>
                    {study.category}
                  </Badge>
                  <Badge className={getDifficultyColor(study.difficulty)}>
                    {study.difficulty}
                  </Badge>
                  {study.compensation && (
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      <DollarSign className="w-3 h-3 mr-1" />
                      {study.compensation}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-4">
                  {study.description}
                </p>
                
                <div className="space-y-3">
                  {/* Duration and Participants */}
                  <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {study.duration_weeks} weeks
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {study.current_participants}/{study.max_participants}
                    </div>
                  </div>
                  
                  {/* Availability Bar */}
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Availability</span>
                      <span>{spotsLeft} spots left</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all ${
                          availabilityPercentage > 50 ? 'bg-green-500' :
                          availabilityPercentage > 20 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${availabilityPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  {/* Researcher */}
                  {study.researcher_name && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Led by {study.researcher_name}
                    </div>
                  )}
                  
                  {/* Action Button */}
                  <Button 
                    className="w-full"
                    disabled={spotsLeft <= 0}
                    onClick={(e) => {
                      e.stopPropagation();
                      onJoinStudy?.(study.id);
                    }}
                  >
                    {spotsLeft <= 0 ? 'Study Full' : 'Join Study'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {studies.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No studies found
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Try adjusting your search terms or filters
          </p>
          <Button variant="outline" onClick={() => {
            setSearchTerm('');
            setSelectedCategory('');
            setSelectedDifficulty('');
          }}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default StudyBrowser;
