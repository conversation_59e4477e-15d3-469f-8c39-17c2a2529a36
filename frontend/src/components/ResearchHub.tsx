/**
 * Research Hub Component
 * 
 * Interface for discovering and participating in supplement research studies
 */

import React, { useState } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Card } from './ui/card';
import { Badge } from './ui/badge';
import { 
  Search, 
  Filter, 
  Users, 
  Clock, 
  TrendingUp,
  FlaskConical,
  BookOpen,
  Award,
  ChevronRight,
  Star,
  Calendar,
  Target
} from 'lucide-react';

interface ResearchStudy {
  id: number;
  title: string;
  description: string;
  category: string;
  duration: string;
  participants: number;
  maxParticipants: number;
  status: 'recruiting' | 'active' | 'completed';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  compensation?: string;
  requirements: string[];
  tags: string[];
  startDate: string;
  estimatedCompletion: string;
  researcherName: string;
  institution: string;
  rating: number;
}

const ResearchHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'discover' | 'my-studies' | 'create'>('discover');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock data
  const studies: ResearchStudy[] = [
    {
      id: 1,
      title: 'Vitamin D3 and Sleep Quality Study',
      description: 'Investigating the relationship between Vitamin D3 supplementation and sleep quality metrics over 12 weeks.',
      category: 'Vitamins',
      duration: '12 weeks',
      participants: 156,
      maxParticipants: 200,
      status: 'recruiting',
      difficulty: 'beginner',
      compensation: '$50 Amazon gift card',
      requirements: ['Age 18-65', 'No current Vitamin D supplementation', 'Sleep tracking device'],
      tags: ['sleep', 'vitamin-d', 'wellness'],
      startDate: '2024-07-01',
      estimatedCompletion: '2024-09-30',
      researcherName: 'Dr. Sarah Johnson',
      institution: 'Stanford University',
      rating: 4.8
    },
    {
      id: 2,
      title: 'Omega-3 Cognitive Performance Research',
      description: 'Examining the effects of high-dose omega-3 supplementation on cognitive performance and memory.',
      category: 'Fatty Acids',
      duration: '16 weeks',
      participants: 89,
      maxParticipants: 150,
      status: 'recruiting',
      difficulty: 'intermediate',
      requirements: ['Age 25-55', 'No fish allergies', 'Cognitive assessment participation'],
      tags: ['cognitive', 'omega-3', 'memory'],
      startDate: '2024-06-15',
      estimatedCompletion: '2024-10-15',
      researcherName: 'Dr. Michael Chen',
      institution: 'Harvard Medical School',
      rating: 4.9
    },
    {
      id: 3,
      title: 'Magnesium and Exercise Recovery',
      description: 'Study on magnesium supplementation effects on muscle recovery and exercise performance.',
      category: 'Minerals',
      duration: '8 weeks',
      participants: 45,
      maxParticipants: 100,
      status: 'active',
      difficulty: 'intermediate',
      requirements: ['Regular exercise routine', 'Age 20-45', 'No magnesium supplementation'],
      tags: ['exercise', 'recovery', 'magnesium'],
      startDate: '2024-05-01',
      estimatedCompletion: '2024-07-01',
      researcherName: 'Dr. Emily Rodriguez',
      institution: 'UCLA',
      rating: 4.7
    }
  ];

  const myStudies = [
    {
      id: 1,
      title: 'Vitamin D3 and Sleep Quality Study',
      progress: 60,
      nextTask: 'Weekly sleep survey',
      dueDate: '2024-06-22'
    },
    {
      id: 2,
      title: 'Omega-3 Cognitive Performance Research',
      progress: 30,
      nextTask: 'Cognitive assessment',
      dueDate: '2024-06-25'
    }
  ];

  const categories = ['all', 'Vitamins', 'Minerals', 'Fatty Acids', 'Herbs', 'Probiotics', 'Amino Acids'];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recruiting': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'completed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Research Hub
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Discover and participate in evidence-based supplement research
            </p>
          </div>
          <Button>
            <FlaskConical className="h-4 w-4 mr-2" />
            Create Study
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {[
            { key: 'discover', label: 'Discover Studies', icon: Search },
            { key: 'my-studies', label: 'My Studies', icon: BookOpen },
            { key: 'create', label: 'Create Study', icon: FlaskConical }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === key
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </div>

        {/* Discover Studies */}
        {activeTab === 'discover' && (
          <div>
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search studies..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>

            {/* Studies Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {studies.map((study) => (
                <Card key={study.id} className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {study.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                        {study.description}
                      </p>
                    </div>
                    <div className="flex items-center ml-4">
                      <Star className="h-4 w-4 text-yellow-400 mr-1" />
                      <span className="text-sm font-medium">{study.rating}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge className={getStatusColor(study.status)}>
                      {study.status}
                    </Badge>
                    <Badge className={getDifficultyColor(study.difficulty)}>
                      {study.difficulty}
                    </Badge>
                    <Badge variant="outline">{study.category}</Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{study.duration}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 text-gray-400 mr-2" />
                      <span>{study.participants}/{study.maxParticipants}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Starts {new Date(study.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <Target className="h-4 w-4 text-gray-400 mr-2" />
                      <span>Ends {new Date(study.estimatedCompletion).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      <strong>Researcher:</strong> {study.researcherName}, {study.institution}
                    </p>
                    {study.compensation && (
                      <p className="text-sm text-green-600 dark:text-green-400">
                        <Award className="h-4 w-4 inline mr-1" />
                        {study.compensation}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-4">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(study.participants / study.maxParticipants) * 100}%` }}
                      />
                    </div>
                    <Button size="sm">
                      Join Study
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* My Studies */}
        {activeTab === 'my-studies' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {myStudies.map((study) => (
              <Card key={study.id} className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {study.title}
                </h3>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
                    <span className="text-sm font-medium">{study.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${study.progress}%` }}
                    />
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                    Next Task
                  </h4>
                  <p className="text-blue-700 dark:text-blue-300 text-sm">
                    {study.nextTask}
                  </p>
                  <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">
                    Due: {new Date(study.dueDate).toLocaleDateString()}
                  </p>
                </div>

                <div className="flex space-x-2">
                  <Button size="sm" className="flex-1">
                    Continue Study
                  </Button>
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Create Study */}
        {activeTab === 'create' && (
          <Card className="p-8">
            <div className="text-center">
              <FlaskConical className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Create Research Study
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Design and launch your own supplement research study
              </p>
              <Button>
                Start Study Builder
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ResearchHub;
