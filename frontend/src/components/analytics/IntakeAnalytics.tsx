/**
 * Intake Analytics Component
 * 
 * Displays comprehensive analytics for supplement intake patterns and adherence
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Target,
  Smile,
  BarChart3,
  Clock,
  Award,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface AdherenceData {
  period_days: number;
  total_supplements: number;
  adherence_rate: number;
  total_expected: number;
  total_taken: number;
  daily_breakdown: Array<{
    date: string;
    expected: number;
    taken: number;
    adherence_rate: number;
  }>;
  supplement_breakdown: Array<{
    supplement_id: number;
    supplement_name: string;
    expected: number;
    taken: number;
    adherence_rate: number;
  }>;
}

interface MoodData {
  period_days: number;
  total_mood_entries: number;
  average_mood_before: number | null;
  average_mood_after: number | null;
  mood_improvement: number | null;
  daily_mood_trend: Array<{
    date: string;
    average_mood_before: number | null;
    average_mood_after: number | null;
    entries_count: number;
  }>;
}

interface StreakData {
  current_streak: number;
  longest_streak: number;
  total_supplements: number;
}

const IntakeAnalytics: React.FC = () => {
  const [adherenceData, setAdherenceData] = useState<AdherenceData | null>(null);
  const [moodData, setMoodData] = useState<MoodData | null>(null);
  const [streakData, setStreakData] = useState<StreakData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState(30);
  const { toast } = useToast();

  const periods = [
    { label: '7 days', value: 7 },
    { label: '30 days', value: 30 },
    { label: '90 days', value: 90 }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      // Fetch adherence analytics
      const adherenceResponse = await fetch(
        `http://api.pills.localhost/api/v1/supplements/analytics/adherence?days=${selectedPeriod}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      
      if (adherenceResponse.ok) {
        const adherenceResult = await adherenceResponse.json();
        setAdherenceData(adherenceResult);
      }

      // Fetch mood correlation
      const moodResponse = await fetch(
        `http://api.pills.localhost/api/v1/supplements/analytics/mood-correlation?days=${selectedPeriod}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      
      if (moodResponse.ok) {
        const moodResult = await moodResponse.json();
        setMoodData(moodResult);
      }

      // Fetch streak data
      const streakResponse = await fetch(
        'http://api.pills.localhost/api/v1/supplements/intake/streak',
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );
      
      if (streakResponse.ok) {
        const streakResult = await streakResponse.json();
        setStreakData(streakResult);
      }

    } catch (error) {
      toast({
        title: "Failed to load analytics",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getAdherenceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600 dark:text-green-400';
    if (rate >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getAdherenceBadgeColor = (rate: number) => {
    if (rate >= 90) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    if (rate >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
  };

  const getMoodIcon = (improvement: number | null) => {
    if (!improvement) return <Smile className="w-4 h-4 text-gray-400" />;
    if (improvement > 0) return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (improvement < 0) return <TrendingDown className="w-4 h-4 text-red-500" />;
    return <Smile className="w-4 h-4 text-gray-400" />;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selector */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Intake Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Track your supplement adherence and patterns
          </p>
        </div>
        <div className="flex gap-2">
          {periods.map((period) => (
            <Button
              key={period.value}
              variant={selectedPeriod === period.value ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedPeriod(period.value)}
            >
              {period.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Overall Adherence */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Overall Adherence
                </p>
                <p className={`text-2xl font-bold ${getAdherenceColor(adherenceData?.adherence_rate || 0)}`}>
                  {adherenceData?.adherence_rate || 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Streak */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                <Award className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Current Streak
                </p>
                <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {streakData?.current_streak || 0} days
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mood Improvement */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                {getMoodIcon(moodData?.mood_improvement)}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Mood Change
                </p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {moodData?.mood_improvement ? 
                    `${moodData.mood_improvement > 0 ? '+' : ''}${moodData.mood_improvement.toFixed(1)}` 
                    : 'N/A'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Intakes */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Intakes
                </p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {adherenceData?.total_taken || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Adherence Breakdown */}
      {adherenceData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Supplement Adherence Breakdown
            </CardTitle>
            <CardDescription>
              Individual supplement adherence rates over the last {selectedPeriod} days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {adherenceData.supplement_breakdown.map((supplement) => (
                <div key={supplement.supplement_id} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {supplement.supplement_name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {supplement.taken} of {supplement.expected} doses taken
                      </p>
                    </div>
                    <Badge className={getAdherenceBadgeColor(supplement.adherence_rate)}>
                      {supplement.adherence_rate}%
                    </Badge>
                  </div>
                  <Progress value={supplement.adherence_rate} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mood Correlation */}
      {moodData && moodData.total_mood_entries > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smile className="w-5 h-5" />
              Mood Correlation Analysis
            </CardTitle>
            <CardDescription>
              How your mood changes with supplement intake
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Average Mood Before
                </p>
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {moodData.average_mood_before?.toFixed(1) || 'N/A'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">out of 10</p>
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Average Mood After
                </p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {moodData.average_mood_after?.toFixed(1) || 'N/A'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">out of 10</p>
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  Improvement
                </p>
                <p className={`text-3xl font-bold ${
                  moodData.mood_improvement && moodData.mood_improvement > 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : moodData.mood_improvement && moodData.mood_improvement < 0
                    ? 'text-red-600 dark:text-red-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {moodData.mood_improvement ? 
                    `${moodData.mood_improvement > 0 ? '+' : ''}${moodData.mood_improvement.toFixed(1)}` 
                    : 'N/A'
                  }
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {moodData.total_mood_entries} entries
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Streak Information */}
      {streakData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Streak Performance
            </CardTitle>
            <CardDescription>
              Your consistency in taking supplements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center p-6 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-lg">
                <Award className="w-12 h-12 text-orange-500 mx-auto mb-3" />
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Current Streak
                </p>
                <p className="text-4xl font-bold text-orange-600 dark:text-orange-400 mb-1">
                  {streakData.current_streak}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {streakData.current_streak === 1 ? 'day' : 'days'}
                </p>
              </div>
              
              <div className="text-center p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg">
                <TrendingUp className="w-12 h-12 text-purple-500 mx-auto mb-3" />
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Longest Streak
                </p>
                <p className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                  {streakData.longest_streak}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {streakData.longest_streak === 1 ? 'day' : 'days'}
                </p>
              </div>
            </div>
            
            {streakData.current_streak === 0 && (
              <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
                  <AlertTriangle className="w-4 h-4" />
                  <p className="text-sm font-medium">
                    Your streak has been broken. Start a new one today!
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default IntakeAnalytics;
