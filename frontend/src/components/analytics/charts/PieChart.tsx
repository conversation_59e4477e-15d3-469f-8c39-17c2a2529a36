/**
 * Pie Chart Component
 * 
 * Reusable pie chart component for categorical data distribution visualization.
 */

import React, { useRef, useEffect } from 'react';
import { useTheme } from 'styled-components';
import styled from 'styled-components';
import * as d3 from 'd3';

// Types
interface PieDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: any;
}

interface PieChartProps {
  data: PieDataPoint[];
  width?: number;
  height?: number;
  innerRadius?: number;
  title?: string;
  showLabels?: boolean;
  showPercentages?: boolean;
  showLegend?: boolean;
  animate?: boolean;
  onSliceClick?: (slice: PieDataPoint) => void;
  onSliceHover?: (slice: PieDataPoint | null) => void;
  className?: string;
}

// Styled components
const ChartContainer = styled.div`
  position: relative;
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: 1rem;
  box-shadow: 0 2px 8px ${props => props.theme.colors.shadow}10;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ChartTitle = styled.h3`
  color: ${props => props.theme.colors.text};
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  text-align: center;
`;

const ChartContent = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const ChartSvg = styled.svg`
  display: block;
  
  .slice {
    cursor: pointer;
    transition: transform 0.2s ease;
    transform-origin: center;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .slice-label {
    fill: ${props => props.theme.colors.text};
    font-size: 12px;
    font-weight: 500;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
  }
  
  .slice-percentage {
    fill: ${props => props.theme.colors.text};
    font-size: 10px;
    text-anchor: middle;
    dominant-baseline: middle;
    pointer-events: none;
  }
`;

const Legend = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.text};
`;

const LegendColor = styled.div<{ $color: string }>`
  width: 12px;
  height: 12px;
  border-radius: 2px;
  background-color: ${props => props.$color};
  flex-shrink: 0;
`;

const LegendLabel = styled.span`
  flex: 1;
`;

const LegendValue = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.textSecondary};
`;

const Tooltip = styled.div<{ $x: number; $y: number; $visible: boolean }>`
  position: absolute;
  background: ${props => props.theme.colors.backgroundSecondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.small};
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.text};
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 4px 12px ${props => props.theme.colors.shadow}20;
  opacity: ${props => props.$visible ? 1 : 0};
  transform: translate(${props => props.$x}px, ${props => props.$y}px);
  transition: opacity 0.2s ease;
  white-space: nowrap;
`;

const PieChart: React.FC<PieChartProps> = ({
  data,
  width = 300,
  height = 300,
  innerRadius = 0,
  title,
  showLabels = true,
  showPercentages = true,
  showLegend = true,
  animate = true,
  onSliceClick,
  onSliceHover,
  className,
}) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltipData, setTooltipData] = React.useState<{
    slice: PieDataPoint | null;
    x: number;
    y: number;
    visible: boolean;
  }>({
    slice: null,
    x: 0,
    y: 0,
    visible: false,
  });

  const radius = Math.min(width, height) / 2 - 10;
  const outerRadius = radius;
  const total = data.reduce((sum, d) => sum + d.value, 0);

  // Generate colors if not provided
  const colorScale = d3.scaleOrdinal(d3.schemeCategory10);
  const dataWithColors = data.map((d, i) => ({
    ...d,
    color: d.color || colorScale(i.toString()),
  }));

  useEffect(() => {
    if (!svgRef.current || data.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create pie generator
    const pie = d3.pie<PieDataPoint>()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3.arc<d3.PieArcDatum<PieDataPoint>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create label arc (for positioning labels)
    const labelArc = d3.arc<d3.PieArcDatum<PieDataPoint>>()
      .innerRadius(outerRadius * 0.7)
      .outerRadius(outerRadius * 0.7);

    // Create main group
    const g = svg.append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    // Create slices
    const slices = g.selectAll('.slice')
      .data(pie(dataWithColors))
      .enter()
      .append('path')
      .attr('class', 'slice')
      .attr('fill', d => d.data.color!)
      .attr('stroke', theme.colors.background)
      .attr('stroke-width', 2);

    // Animate slices
    if (animate) {
      slices
        .attr('d', d => {
          const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
          return arc(interpolate(0) as any);
        })
        .transition()
        .duration(800)
        .delay((d, i) => i * 100)
        .attrTween('d', function(d) {
          const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
          return (t: number) => arc(interpolate(t) as any)!;
        });
    } else {
      slices.attr('d', arc as any);
    }

    // Add labels
    if (showLabels) {
      const labels = g.selectAll('.slice-label')
        .data(pie(dataWithColors))
        .enter()
        .append('text')
        .attr('class', 'slice-label')
        .attr('transform', d => `translate(${labelArc.centroid(d)})`)
        .style('opacity', animate ? 0 : 1)
        .text(d => {
          const percentage = ((d.data.value / total) * 100).toFixed(1);
          if (showPercentages) {
            return `${d.data.label}\n${percentage}%`;
          }
          return d.data.label;
        });

      if (animate) {
        labels.transition()
          .delay(800)
          .duration(300)
          .style('opacity', 1);
      }
    }

    // Add interactions
    slices
      .on('click', function(event, d) {
        onSliceClick?.(d.data);
      })
      .on('mouseover', function(event, d) {
        const [mouseX, mouseY] = d3.pointer(event, document.body);
        setTooltipData({
          slice: d.data,
          x: mouseX,
          y: mouseY - 10,
          visible: true,
        });
        onSliceHover?.(d.data);
      })
      .on('mouseout', function() {
        setTooltipData(prev => ({ ...prev, visible: false }));
        onSliceHover?.(null);
      });

  }, [data, width, height, innerRadius, outerRadius, radius, showLabels, showPercentages, animate, theme, total, dataWithColors, onSliceClick, onSliceHover]);

  const formatTooltipValue = (value: number) => {
    const percentage = ((value / total) * 100).toFixed(1);
    return `${value.toLocaleString()} (${percentage}%)`;
  };

  return (
    <ChartContainer className={className}>
      {title && <ChartTitle>{title}</ChartTitle>}
      <ChartContent>
        <ChartSvg
          ref={svgRef}
          width={width}
          height={height}
          viewBox={`0 0 ${width} ${height}`}
        />
        {showLegend && (
          <Legend>
            {dataWithColors.map((item, index) => (
              <LegendItem key={index}>
                <LegendColor $color={item.color!} />
                <LegendLabel>{item.label}</LegendLabel>
                <LegendValue>
                  {((item.value / total) * 100).toFixed(1)}%
                </LegendValue>
              </LegendItem>
            ))}
          </Legend>
        )}
      </ChartContent>
      <Tooltip
        $x={tooltipData.x}
        $y={tooltipData.y}
        $visible={tooltipData.visible}
      >
        {tooltipData.slice && (
          <>
            <div><strong>{tooltipData.slice.label}</strong></div>
            <div>{formatTooltipValue(tooltipData.slice.value)}</div>
          </>
        )}
      </Tooltip>
    </ChartContainer>
  );
};

export default PieChart;
