/**
 * Bar Chart Component
 * 
 * Reusable bar chart component for categorical data visualization.
 */

import React, { useRef, useEffect } from 'react';
import { useTheme } from 'styled-components';
import styled from 'styled-components';
import * as d3 from 'd3';

// Types
interface BarDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: any;
}

interface BarChartProps {
  data: BarDataPoint[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  xAxisLabel?: string;
  yAxisLabel?: string;
  title?: string;
  orientation?: 'vertical' | 'horizontal';
  showValues?: boolean;
  animate?: boolean;
  onBarClick?: (bar: BarDataPoint) => void;
  onBarHover?: (bar: BarDataPoint | null) => void;
  className?: string;
}

// Styled components
const ChartContainer = styled.div`
  position: relative;
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: 1rem;
  box-shadow: 0 2px 8px ${props => props.theme.colors.shadow}10;
`;

const ChartTitle = styled.h3`
  color: ${props => props.theme.colors.text};
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  text-align: center;
`;

const ChartSvg = styled.svg`
  display: block;
  width: 100%;
  height: auto;
  
  .bar {
    cursor: pointer;
    transition: opacity 0.2s ease;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  .bar-value {
    fill: ${props => props.theme.colors.text};
    font-size: 12px;
    font-weight: 500;
    text-anchor: middle;
    dominant-baseline: middle;
  }
  
  .axis {
    .domain {
      stroke: ${props => props.theme.colors.border};
    }
    
    .tick line {
      stroke: ${props => props.theme.colors.border};
    }
    
    .tick text {
      fill: ${props => props.theme.colors.textSecondary};
      font-size: 12px;
    }
  }
  
  .axis-label {
    fill: ${props => props.theme.colors.text};
    font-size: 14px;
    font-weight: 500;
  }
`;

const Tooltip = styled.div<{ $x: number; $y: number; $visible: boolean }>`
  position: absolute;
  background: ${props => props.theme.colors.backgroundSecondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.small};
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.text};
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 4px 12px ${props => props.theme.colors.shadow}20;
  opacity: ${props => props.$visible ? 1 : 0};
  transform: translate(${props => props.$x}px, ${props => props.$y}px);
  transition: opacity 0.2s ease;
  white-space: nowrap;
`;

const BarChart: React.FC<BarChartProps> = ({
  data,
  width = 600,
  height = 300,
  margin = { top: 20, right: 30, bottom: 60, left: 60 },
  xAxisLabel,
  yAxisLabel,
  title,
  orientation = 'vertical',
  showValues = true,
  animate = true,
  onBarClick,
  onBarHover,
  className,
}) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltipData, setTooltipData] = React.useState<{
    bar: BarDataPoint | null;
    x: number;
    y: number;
    visible: boolean;
  }>({
    bar: null,
    x: 0,
    y: 0,
    visible: false,
  });

  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  useEffect(() => {
    if (!svgRef.current || data.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create scales
    let xScale: any, yScale: any;

    if (orientation === 'vertical') {
      xScale = d3.scaleBand()
        .domain(data.map(d => d.label))
        .range([0, innerWidth])
        .padding(0.1);

      yScale = d3.scaleLinear()
        .domain([0, d3.max(data, d => d.value) as number])
        .nice()
        .range([innerHeight, 0]);
    } else {
      xScale = d3.scaleLinear()
        .domain([0, d3.max(data, d => d.value) as number])
        .nice()
        .range([0, innerWidth]);

      yScale = d3.scaleBand()
        .domain(data.map(d => d.label))
        .range([0, innerHeight])
        .padding(0.1);
    }

    // Create main group
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create bars
    const bars = g.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('fill', d => d.color || theme.colors.primary);

    if (orientation === 'vertical') {
      bars
        .attr('x', d => xScale(d.label))
        .attr('width', xScale.bandwidth())
        .attr('y', innerHeight)
        .attr('height', 0);

      // Animate bars
      if (animate) {
        bars.transition()
          .duration(800)
          .delay((d, i) => i * 100)
          .attr('y', d => yScale(d.value))
          .attr('height', d => innerHeight - yScale(d.value));
      } else {
        bars
          .attr('y', d => yScale(d.value))
          .attr('height', d => innerHeight - yScale(d.value));
      }
    } else {
      bars
        .attr('x', 0)
        .attr('width', 0)
        .attr('y', d => yScale(d.label))
        .attr('height', yScale.bandwidth());

      // Animate bars
      if (animate) {
        bars.transition()
          .duration(800)
          .delay((d, i) => i * 100)
          .attr('width', d => xScale(d.value));
      } else {
        bars.attr('width', d => xScale(d.value));
      }
    }

    // Add value labels
    if (showValues) {
      const labels = g.selectAll('.bar-value')
        .data(data)
        .enter()
        .append('text')
        .attr('class', 'bar-value')
        .style('opacity', animate ? 0 : 1);

      if (orientation === 'vertical') {
        labels
          .attr('x', d => xScale(d.label) + xScale.bandwidth() / 2)
          .attr('y', d => yScale(d.value) - 5)
          .text(d => d.value.toLocaleString());
      } else {
        labels
          .attr('x', d => xScale(d.value) + 5)
          .attr('y', d => yScale(d.label) + yScale.bandwidth() / 2)
          .text(d => d.value.toLocaleString());
      }

      if (animate) {
        labels.transition()
          .delay(800)
          .duration(300)
          .style('opacity', 1);
      }
    }

    // Add interactions
    bars
      .on('click', function(event, d) {
        onBarClick?.(d);
      })
      .on('mouseover', function(event, d) {
        const [mouseX, mouseY] = d3.pointer(event, document.body);
        setTooltipData({
          bar: d,
          x: mouseX,
          y: mouseY - 10,
          visible: true,
        });
        onBarHover?.(d);
      })
      .on('mouseout', function() {
        setTooltipData(prev => ({ ...prev, visible: false }));
        onBarHover?.(null);
      });

    // Add axes
    if (orientation === 'vertical') {
      // X axis
      const xAxis = d3.axisBottom(xScale);
      g.append('g')
        .attr('class', 'axis x-axis')
        .attr('transform', `translate(0,${innerHeight})`)
        .call(xAxis)
        .selectAll('text')
        .style('text-anchor', 'end')
        .attr('dx', '-.8em')
        .attr('dy', '.15em')
        .attr('transform', 'rotate(-45)');

      // Y axis
      const yAxis = d3.axisLeft(yScale);
      g.append('g')
        .attr('class', 'axis y-axis')
        .call(yAxis);
    } else {
      // X axis
      const xAxis = d3.axisBottom(xScale);
      g.append('g')
        .attr('class', 'axis x-axis')
        .attr('transform', `translate(0,${innerHeight})`)
        .call(xAxis);

      // Y axis
      const yAxis = d3.axisLeft(yScale);
      g.append('g')
        .attr('class', 'axis y-axis')
        .call(yAxis);
    }

    // Add axis labels
    if (xAxisLabel) {
      g.append('text')
        .attr('class', 'axis-label x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + margin.bottom - 5)
        .text(xAxisLabel);
    }

    if (yAxisLabel) {
      g.append('text')
        .attr('class', 'axis-label y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -margin.left + 15)
        .text(yAxisLabel);
    }

  }, [data, width, height, margin, orientation, showValues, animate, theme, innerWidth, innerHeight, xAxisLabel, yAxisLabel, onBarClick, onBarHover]);

  const formatTooltipValue = (value: number) => {
    return value.toLocaleString(undefined, { 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2 
    });
  };

  return (
    <ChartContainer className={className}>
      {title && <ChartTitle>{title}</ChartTitle>}
      <ChartSvg
        ref={svgRef}
        width={width}
        height={height}
        viewBox={`0 0 ${width} ${height}`}
      />
      <Tooltip
        $x={tooltipData.x}
        $y={tooltipData.y}
        $visible={tooltipData.visible}
      >
        {tooltipData.bar && (
          <>
            <div><strong>{tooltipData.bar.label}</strong></div>
            <div>{formatTooltipValue(tooltipData.bar.value)}</div>
          </>
        )}
      </Tooltip>
    </ChartContainer>
  );
};

export default BarChart;
