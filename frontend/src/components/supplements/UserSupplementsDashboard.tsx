/**
 * User Supplements Dashboard Component
 * 
 * Displays user's personal supplement schedule and intake tracking
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchUserSupplements,
  trackSupplementIntake,
  selectSupplements,
  selectSupplementsLoading,
  selectSupplementsError
} from '@/store/slices/supplementSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  Plus, 
  Pill, 
  TrendingUp,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface UserSupplementsDashboardProps {
  onAddSupplement?: () => void;
}

const UserSupplementsDashboard: React.FC<UserSupplementsDashboardProps> = ({
  onAddSupplement
}) => {
  const dispatch = useAppDispatch();
  const userSupplements = useAppSelector(selectSupplements);
  const loading = useAppSelector(selectSupplementsLoading);
  const error = useAppSelector(selectSupplementsError);
  const { toast } = useToast();

  const [todayIntakes, setTodayIntakes] = useState<Set<number>>(new Set());

  useEffect(() => {
    dispatch(fetchUserSupplements());
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Error loading supplements",
        description: error,
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const handleLogIntake = async (userSupplementId: number, supplementName: string) => {
    try {
      await dispatch(trackSupplementIntake({
        user_supplement_id: userSupplementId,
        taken_at: new Date().toISOString(),
        notes: `Logged via dashboard`
      })).unwrap();

      setTodayIntakes(prev => new Set([...prev, userSupplementId]));
      
      toast({
        title: "Intake logged!",
        description: `${supplementName} has been marked as taken.`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to log intake",
        description: error || "Please try again.",
        variant: "destructive",
      });
    }
  };

  const getTimeOfDayColor = (timeOfDay?: string) => {
    switch (timeOfDay?.toLowerCase()) {
      case 'morning': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'afternoon': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'evening': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'night': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getFrequencyText = (frequency: string) => {
    switch (frequency.toLowerCase()) {
      case 'daily': return 'Daily';
      case 'twice_daily': return '2x Daily';
      case 'three_times_daily': return '3x Daily';
      case 'weekly': return 'Weekly';
      case 'as_needed': return 'As Needed';
      default: return frequency;
    }
  };

  // Calculate today's progress
  const totalSupplements = userSupplements.length;
  const completedToday = todayIntakes.size;
  const progressPercentage = totalSupplements > 0 ? (completedToday / totalSupplements) * 100 : 0;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Supplements
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Track your daily supplement routine
          </p>
        </div>
        <Button onClick={onAddSupplement} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add Supplement
        </Button>
      </div>

      {/* Today's Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Today's Progress
          </CardTitle>
          <CardDescription>
            {format(new Date(), 'EEEE, MMMM do, yyyy')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {completedToday} of {totalSupplements} supplements taken
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            {progressPercentage === 100 && totalSupplements > 0 && (
              <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">Great job! All supplements taken today.</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Supplements List */}
      {userSupplements.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {userSupplements.map((userSupplement) => {
            const isCompleted = todayIntakes.has(userSupplement.id);
            const supplement = userSupplement.supplement;
            
            return (
              <Card 
                key={userSupplement.id} 
                className={`transition-all ${isCompleted ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' : ''}`}
              >
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Pill className="w-5 h-5" />
                        {userSupplement.custom_name || supplement.name}
                        {isCompleted && (
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                        )}
                      </CardTitle>
                      {supplement.brand && (
                        <CardDescription>by {supplement.brand}</CardDescription>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Settings className="w-3 h-3" />
                      Edit
                    </Button>
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge className={getTimeOfDayColor(userSupplement.time_of_day)}>
                      <Clock className="w-3 h-3 mr-1" />
                      {userSupplement.time_of_day || 'Anytime'}
                    </Badge>
                    <Badge variant="outline">
                      {getFrequencyText(userSupplement.frequency)}
                    </Badge>
                    <Badge variant="secondary">
                      {userSupplement.dosage}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {userSupplement.notes && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                      {userSupplement.notes}
                    </p>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Started {format(new Date(userSupplement.start_date), 'MMM d, yyyy')}
                    </div>
                    
                    {!isCompleted ? (
                      <Button
                        size="sm"
                        onClick={() => handleLogIntake(userSupplement.id, supplement.name)}
                        className="flex items-center gap-1"
                      >
                        <CheckCircle className="w-3 h-3" />
                        Mark Taken
                      </Button>
                    ) : (
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        /* Empty State */
        <Card>
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <Pill className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No supplements in your routine
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Start building your personalized supplement routine by adding your first supplement.
            </p>
            <Button onClick={onAddSupplement} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Your First Supplement
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      {userSupplements.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Supplements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userSupplements.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Today's Completion
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(progressPercentage)}%</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Current Streak
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">7 days</div>
              <p className="text-xs text-gray-600 dark:text-gray-400">Keep it up!</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default UserSupplementsDashboard;
