/**
 * Supplement Catalog Component
 * 
 * Displays searchable catalog of available supplements with filtering and details
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchSupplements, 
  selectSupplements, 
  selectSupplementsLoading, 
  selectSupplementsError,
  updateFilters,
  selectSupplementsFilters
} from '@/store/slices/supplementSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Filter, Star, Clock, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SupplementCatalogProps {
  onSelectSupplement?: (supplement: any) => void;
  showAddButton?: boolean;
}

const SupplementCatalog: React.FC<SupplementCatalogProps> = ({
  onSelectSupplement,
  showAddButton = true
}) => {
  const dispatch = useAppDispatch();
  const supplements = useAppSelector(selectSupplements);
  const loading = useAppSelector(selectSupplementsLoading);
  const error = useAppSelector(selectSupplementsError);
  const filters = useAppSelector(selectSupplementsFilters);
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState(filters.search);
  const [selectedCategory, setSelectedCategory] = useState(filters.category);

  // Categories for filtering
  const categories = [
    'All',
    'vitamins',
    'minerals',
    'omega3',
    'probiotics',
    'herbs',
    'adaptogens',
    'antioxidants'
  ];

  useEffect(() => {
    dispatch(fetchSupplements({ 
      search: searchTerm, 
      category: selectedCategory === 'All' ? undefined : selectedCategory 
    }));
  }, [dispatch, searchTerm, selectedCategory]);

  useEffect(() => {
    if (error) {
      toast({
        title: "Error loading supplements",
        description: error,
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    dispatch(updateFilters({ search: value }));
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    dispatch(updateFilters({ category: category === 'All' ? '' : category }));
  };

  const getEvidenceLevelColor = (level?: string) => {
    switch (level) {
      case 'high': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getCategoryColor = (category?: string) => {
    const colors: Record<string, string> = {
      vitamins: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      minerals: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      omega3: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
      probiotics: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      herbs: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300',
      adaptogens: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
      antioxidants: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
    };
    return colors[category || ''] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Supplement Catalog
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Discover and learn about supplements backed by research
          </p>
        </div>
        {showAddButton && (
          <Button className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add New Supplement
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search supplements..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2 overflow-x-auto pb-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategoryChange(category)}
              className="whitespace-nowrap"
            >
              {category === 'All' ? (
                <>
                  <Filter className="w-3 h-3 mr-1" />
                  All
                </>
              ) : (
                category.charAt(0).toUpperCase() + category.slice(1)
              )}
            </Button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Supplements Grid */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {supplements.map((supplement) => (
            <Card 
              key={supplement.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => onSelectSupplement?.(supplement)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{supplement.name}</CardTitle>
                    {supplement.brand && (
                      <CardDescription className="text-sm text-gray-600 dark:text-gray-400">
                        by {supplement.brand}
                      </CardDescription>
                    )}
                  </div>
                  {supplement.evidence_level && (
                    <Badge className={getEvidenceLevelColor(supplement.evidence_level)}>
                      <Star className="w-3 h-3 mr-1" />
                      {supplement.evidence_level}
                    </Badge>
                  )}
                </div>
                
                <div className="flex gap-2 flex-wrap">
                  {supplement.category && (
                    <Badge variant="secondary" className={getCategoryColor(supplement.category)}>
                      {supplement.category}
                    </Badge>
                  )}
                  {supplement.default_dosage && (
                    <Badge variant="outline" className="text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {supplement.default_dosage}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                  {supplement.description || 'No description available.'}
                </p>
                
                <div className="mt-4 flex justify-between items-center">
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <Users className="w-3 h-3 mr-1" />
                    Research backed
                  </div>
                  
                  {showAddButton && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Handle add to schedule
                        toast({
                          title: "Add to Schedule",
                          description: `${supplement.name} will be added to your supplement schedule.`,
                        });
                      }}
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && supplements.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No supplements found
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Try adjusting your search terms or filters
          </p>
          <Button variant="outline" onClick={() => {
            setSearchTerm('');
            setSelectedCategory('All');
            handleSearch('');
            handleCategoryChange('All');
          }}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default SupplementCatalog;
