/**
 * Intake Tracker Component
 * 
 * Advanced intake tracking with mood logging, scheduling, and analytics
 */

import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  trackSupplementIntake,
  fetchUserSupplements,
  selectUserSupplements,
  selectSupplementsLoading
} from '@/store/slices/supplementSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { 
  CheckCircle, 
  Clock, 
  Smile, 
  Frown, 
  Meh,
  Calendar,
  TrendingUp,
  AlertCircle,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface IntakeEntry {
  user_supplement_id: number;
  supplement_name: string;
  dosage: string;
  time_of_day?: string;
  taken: boolean;
  mood_before?: number;
  mood_after?: number;
  notes?: string;
  taken_at?: string;
}

const IntakeTracker: React.FC = () => {
  const dispatch = useAppDispatch();
  const userSupplements = useAppSelector(selectUserSupplements);
  const loading = useAppSelector(selectSupplementsLoading);
  const { toast } = useToast();

  const [todayIntakes, setTodayIntakes] = useState<Map<number, IntakeEntry>>(new Map());
  const [selectedSupplement, setSelectedSupplement] = useState<number | null>(null);
  const [moodBefore, setMoodBefore] = useState<number>(5);
  const [moodAfter, setMoodAfter] = useState<number>(5);
  const [notes, setNotes] = useState('');
  const [showMoodTracking, setShowMoodTracking] = useState(false);

  useEffect(() => {
    dispatch(fetchUserSupplements());
  }, [dispatch]);

  useEffect(() => {
    // Initialize today's intake tracking
    const intakeMap = new Map<number, IntakeEntry>();
    userSupplements.forEach(userSupplement => {
      intakeMap.set(userSupplement.id, {
        user_supplement_id: userSupplement.id,
        supplement_name: userSupplement.custom_name || userSupplement.supplement.name,
        dosage: userSupplement.dosage,
        time_of_day: userSupplement.time_of_day,
        taken: false
      });
    });
    setTodayIntakes(intakeMap);
  }, [userSupplements]);

  const handleQuickLog = async (userSupplementId: number) => {
    try {
      await dispatch(trackSupplementIntake({
        user_supplement_id: userSupplementId,
        taken_at: new Date().toISOString(),
        notes: 'Quick log'
      })).unwrap();

      // Update local state
      setTodayIntakes(prev => {
        const updated = new Map(prev);
        const entry = updated.get(userSupplementId);
        if (entry) {
          entry.taken = true;
          entry.taken_at = new Date().toISOString();
        }
        return updated;
      });

      const supplement = userSupplements.find(s => s.id === userSupplementId);
      toast({
        title: "Intake logged!",
        description: `${supplement?.supplement.name} marked as taken.`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to log intake",
        description: error || "Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDetailedLog = async () => {
    if (!selectedSupplement) return;

    try {
      await dispatch(trackSupplementIntake({
        user_supplement_id: selectedSupplement,
        taken_at: new Date().toISOString(),
        mood_before: showMoodTracking ? moodBefore : undefined,
        mood_after: showMoodTracking ? moodAfter : undefined,
        notes: notes || undefined
      })).unwrap();

      // Update local state
      setTodayIntakes(prev => {
        const updated = new Map(prev);
        const entry = updated.get(selectedSupplement);
        if (entry) {
          entry.taken = true;
          entry.taken_at = new Date().toISOString();
          entry.mood_before = showMoodTracking ? moodBefore : undefined;
          entry.mood_after = showMoodTracking ? moodAfter : undefined;
          entry.notes = notes || undefined;
        }
        return updated;
      });

      // Reset form
      setSelectedSupplement(null);
      setMoodBefore(5);
      setMoodAfter(5);
      setNotes('');
      setShowMoodTracking(false);

      const supplement = userSupplements.find(s => s.id === selectedSupplement);
      toast({
        title: "Detailed intake logged!",
        description: `${supplement?.supplement.name} logged with additional details.`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to log intake",
        description: error || "Please try again.",
        variant: "destructive",
      });
    }
  };

  const getMoodIcon = (mood: number) => {
    if (mood <= 3) return <Frown className="w-4 h-4 text-red-500" />;
    if (mood <= 7) return <Meh className="w-4 h-4 text-yellow-500" />;
    return <Smile className="w-4 h-4 text-green-500" />;
  };

  const getTimeOfDayColor = (timeOfDay?: string) => {
    switch (timeOfDay?.toLowerCase()) {
      case 'morning': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'afternoon': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'evening': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'night': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const completedCount = Array.from(todayIntakes.values()).filter(entry => entry.taken).length;
  const totalCount = todayIntakes.size;
  const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="animate-pulse">
          <CardHeader>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Today's Progress Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Today's Intake Progress
          </CardTitle>
          <CardDescription>
            {format(new Date(), 'EEEE, MMMM do, yyyy')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {completedCount} of {totalCount} supplements taken
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            {progressPercentage === 100 && totalCount > 0 && (
              <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">Perfect day! All supplements taken.</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Intake Logging */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Quick Log
          </CardTitle>
          <CardDescription>
            Tap to quickly mark supplements as taken
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from(todayIntakes.values()).map((entry) => (
              <div
                key={entry.user_supplement_id}
                className={`p-4 border rounded-lg transition-all ${
                  entry.taken 
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' 
                    : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                }`}
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {entry.supplement_name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {entry.dosage}
                    </p>
                  </div>
                  {entry.taken && (
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  )}
                </div>

                <div className="flex justify-between items-center">
                  <div className="flex gap-2">
                    {entry.time_of_day && (
                      <Badge className={getTimeOfDayColor(entry.time_of_day)}>
                        <Clock className="w-3 h-3 mr-1" />
                        {entry.time_of_day}
                      </Badge>
                    )}
                    {entry.taken && entry.taken_at && (
                      <Badge variant="outline" className="text-xs">
                        {format(new Date(entry.taken_at), 'HH:mm')}
                      </Badge>
                    )}
                  </div>

                  {!entry.taken ? (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedSupplement(entry.user_supplement_id)}
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Details
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleQuickLog(entry.user_supplement_id)}
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Take
                      </Button>
                    </div>
                  ) : (
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                      Completed
                    </Badge>
                  )}
                </div>

                {/* Show mood and notes if logged */}
                {entry.taken && (entry.mood_before || entry.mood_after || entry.notes) && (
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex gap-4 text-xs text-gray-600 dark:text-gray-400">
                      {entry.mood_before && (
                        <div className="flex items-center gap-1">
                          {getMoodIcon(entry.mood_before)}
                          Before: {entry.mood_before}/10
                        </div>
                      )}
                      {entry.mood_after && (
                        <div className="flex items-center gap-1">
                          {getMoodIcon(entry.mood_after)}
                          After: {entry.mood_after}/10
                        </div>
                      )}
                    </div>
                    {entry.notes && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        "{entry.notes}"
                      </p>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Logging Modal/Form */}
      {selectedSupplement && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Detailed Intake Log
            </CardTitle>
            <CardDescription>
              {todayIntakes.get(selectedSupplement)?.supplement_name}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Mood Tracking Toggle */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Track mood changes</label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowMoodTracking(!showMoodTracking)}
              >
                {showMoodTracking ? 'Hide' : 'Show'} Mood Tracking
              </Button>
            </div>

            {/* Mood Sliders */}
            {showMoodTracking && (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium flex items-center gap-2 mb-2">
                    {getMoodIcon(moodBefore)}
                    Mood Before Taking (1-10)
                  </label>
                  <Slider
                    value={[moodBefore]}
                    onValueChange={(value) => setMoodBefore(value[0])}
                    max={10}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Current: {moodBefore}/10
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium flex items-center gap-2 mb-2">
                    {getMoodIcon(moodAfter)}
                    Expected Mood After (1-10)
                  </label>
                  <Slider
                    value={[moodAfter]}
                    onValueChange={(value) => setMoodAfter(value[0])}
                    max={10}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Expected: {moodAfter}/10
                  </div>
                </div>
              </div>
            )}

            {/* Notes */}
            <div>
              <label className="text-sm font-medium mb-2 block">
                Notes (optional)
              </label>
              <Textarea
                placeholder="How are you feeling? Any side effects? Context about taking this supplement..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                onClick={handleDetailedLog}
                className="flex-1"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Log Intake
              </Button>
              <Button
                variant="outline"
                onClick={() => setSelectedSupplement(null)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {totalCount === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No supplements to track today
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Add supplements to your routine to start tracking your daily intake.
            </p>
            <Button variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              Add Supplements
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default IntakeTracker;
