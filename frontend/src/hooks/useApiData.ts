/**
 * Custom React Hook for API Data Management
 * 
 * Provides caching, automatic refresh, and error handling for API calls
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseApiDataOptions {
  refreshInterval?: number; // milliseconds
  retryAttempts?: number;
  retryDelay?: number;
  cacheTimeout?: number;
}

interface ApiDataState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refresh: () => Promise<void>;
  isStale: boolean;
}

// Simple in-memory cache
const cache = new Map<string, { data: any; timestamp: number; timeout: number }>();

export function useApiData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseApiDataOptions = {}
): ApiDataState<T> {
  const {
    refreshInterval = 30000, // 30 seconds
    retryAttempts = 3,
    retryDelay = 1000,
    cacheTimeout = 60000 // 1 minute
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const refreshTimeoutRef = useRef<NodeJS.Timeout>();
  const mountedRef = useRef(true);

  // Check if cached data is still valid
  const getCachedData = useCallback(() => {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.timeout) {
      return cached.data;
    }
    return null;
  }, [key]);

  // Store data in cache
  const setCachedData = useCallback((newData: T) => {
    cache.set(key, {
      data: newData,
      timestamp: Date.now(),
      timeout: cacheTimeout
    });
  }, [key, cacheTimeout]);

  // Check if data is stale
  const isStale = lastUpdated ? Date.now() - lastUpdated.getTime() > cacheTimeout : true;

  // Fetch data with retry logic
  const fetchData = useCallback(async (attempt = 1): Promise<void> => {
    if (!mountedRef.current) return;

    try {
      setLoading(true);
      setError(null);

      // Try to use cached data first
      const cachedData = getCachedData();
      if (cachedData && attempt === 1) {
        setData(cachedData);
        setLastUpdated(new Date());
        setLoading(false);
        return;
      }

      const result = await fetcher();
      
      if (!mountedRef.current) return;

      setData(result);
      setCachedData(result);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      if (!mountedRef.current) return;

      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      
      if (attempt < retryAttempts) {
        console.warn(`API call failed (attempt ${attempt}/${retryAttempts}):`, errorMessage);
        
        retryTimeoutRef.current = setTimeout(() => {
          fetchData(attempt + 1);
        }, retryDelay * attempt);
      } else {
        console.error('API call failed after all retries:', errorMessage);
        setError(errorMessage);
        
        // Try to use cached data as fallback
        const cachedData = getCachedData();
        if (cachedData) {
          setData(cachedData);
          setError(`Using cached data: ${errorMessage}`);
        }
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [fetcher, retryAttempts, retryDelay, getCachedData, setCachedData]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    // Clear cache for this key to force fresh data
    cache.delete(key);
    await fetchData();
  }, [key, fetchData]);

  // Set up automatic refresh
  useEffect(() => {
    if (refreshInterval > 0) {
      refreshTimeoutRef.current = setInterval(() => {
        fetchData();
      }, refreshInterval);
    }

    return () => {
      if (refreshTimeoutRef.current) {
        clearInterval(refreshTimeoutRef.current);
      }
    };
  }, [fetchData, refreshInterval]);

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (refreshTimeoutRef.current) {
        clearInterval(refreshTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refresh,
    isStale
  };
}

// Specialized hooks for common API calls
export function useSupplements() {
  return useApiData(
    'supplements',
    async () => {
      const response = await fetch('http://api.pills.localhost:9080/api/v1/supplements/catalog');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return response.json();
    },
    { refreshInterval: 60000 } // 1 minute
  );
}

export function useDashboardStats() {
  return useApiData(
    'dashboard-stats',
    async () => {
      const response = await fetch('http://api.pills.localhost:9080/api/v1/supplements/dashboard/stats');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return response.json();
    },
    { refreshInterval: 30000 } // 30 seconds
  );
}

export function useResearchStudies() {
  return useApiData(
    'research-studies',
    async () => {
      const response = await fetch('http://api.pills.localhost:9080/api/v1/research/studies');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return response.json();
    },
    { refreshInterval: 120000 } // 2 minutes
  );
}

export function useHealthCheck() {
  return useApiData(
    'health-check',
    async () => {
      const response = await fetch('http://api.pills.localhost:9080/health/');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return response.json();
    },
    { refreshInterval: 10000 } // 10 seconds
  );
}
