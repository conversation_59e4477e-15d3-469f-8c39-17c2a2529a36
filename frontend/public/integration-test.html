<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="description" content="Supplement Tracker Integration Test" />
    <title>Supplement Tracker - Live Integration Test</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
      .btn {
        @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50;
      }
      
      .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2;
      }
      
      .btn-secondary {
        @apply bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 px-4 py-2;
      }
      
      .btn-success {
        @apply bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2;
      }
      
      .card {
        @apply rounded-lg border bg-white shadow-sm p-6;
      }
      
      .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }
      
      .badge-success {
        @apply bg-green-100 text-green-800;
      }
      
      .badge-error {
        @apply bg-red-100 text-red-800;
      }
      
      .badge-warning {
        @apply bg-yellow-100 text-yellow-800;
      }
      
      .loading {
        @apply animate-spin;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="min-h-screen">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <div class="flex items-center">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center text-white text-lg mr-3">🧪</div>
              <h1 class="text-xl font-semibold text-gray-900">Live Integration Test</h1>
            </div>
            <div class="flex space-x-2">
              <a href="/ux-demo.html" class="btn btn-secondary">UX Demo</a>
              <a href="/" class="btn btn-secondary">← Back to Home</a>
            </div>
          </div>
        </div>
      </header>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Test Controls -->
        <div class="card mb-8">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">Integration Test Suite</h2>
            <button id="runAllTests" class="btn btn-primary">
              <span id="testSpinner" class="hidden loading mr-2">⟳</span>
              Run All Tests
            </button>
          </div>
          <p class="text-gray-600">
            This page tests all backend API endpoints and frontend integrations in real-time.
          </p>
        </div>

        <!-- Test Results Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- API Tests -->
          <div class="card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">API Endpoint Tests</h3>
            <div class="space-y-4">
              <div id="healthTest" class="test-item">
                <div class="flex items-center justify-between">
                  <span class="font-medium">Health Check</span>
                  <span class="badge badge-warning">Pending</span>
                </div>
                <div class="text-sm text-gray-600 mt-1">GET /health/</div>
              </div>
              
              <div id="supplementsTest" class="test-item">
                <div class="flex items-center justify-between">
                  <span class="font-medium">Supplements Catalog</span>
                  <span class="badge badge-warning">Pending</span>
                </div>
                <div class="text-sm text-gray-600 mt-1">GET /api/v1/supplements/catalog</div>
              </div>
              
              <div id="dashboardTest" class="test-item">
                <div class="flex items-center justify-between">
                  <span class="font-medium">Dashboard Stats</span>
                  <span class="badge badge-warning">Pending</span>
                </div>
                <div class="text-sm text-gray-600 mt-1">GET /api/v1/supplements/dashboard/stats</div>
              </div>
              
              <div id="researchTest" class="test-item">
                <div class="flex items-center justify-between">
                  <span class="font-medium">Research Studies</span>
                  <span class="badge badge-warning">Pending</span>
                </div>
                <div class="text-sm text-gray-600 mt-1">GET /api/v1/research/studies</div>
              </div>
            </div>
          </div>

          <!-- Data Display -->
          <div class="card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Live Data Preview</h3>
            <div id="dataPreview" class="space-y-4">
              <div class="text-gray-500 text-center py-8">
                Run tests to see live data...
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Results -->
        <div class="card mt-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Test Results Log</h3>
          <div id="testLog" class="bg-gray-100 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
            <div class="text-gray-500">Test log will appear here...</div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const API_BASE = 'http://api.pills.localhost:9080';
      
      function log(message, type = 'info') {
        const logElement = document.getElementById('testLog');
        const timestamp = new Date().toLocaleTimeString();
        const color = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-gray-700';
        logElement.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
        logElement.scrollTop = logElement.scrollHeight;
      }
      
      function updateTestStatus(testId, status, data = null) {
        const testElement = document.getElementById(testId);
        const badge = testElement.querySelector('.badge');
        
        if (status === 'success') {
          badge.className = 'badge badge-success';
          badge.textContent = 'Pass';
        } else if (status === 'error') {
          badge.className = 'badge badge-error';
          badge.textContent = 'Fail';
        } else {
          badge.className = 'badge badge-warning';
          badge.textContent = 'Testing...';
        }
      }
      
      async function testEndpoint(name, url, testId) {
        log(`Testing ${name}...`);
        updateTestStatus(testId, 'testing');
        
        try {
          const response = await fetch(url);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          
          const data = await response.json();
          log(`✅ ${name} - Success`, 'success');
          updateTestStatus(testId, 'success', data);
          return data;
        } catch (error) {
          log(`❌ ${name} - Error: ${error.message}`, 'error');
          updateTestStatus(testId, 'error');
          return null;
        }
      }
      
      function displayData(data) {
        const preview = document.getElementById('dataPreview');
        
        if (!data.supplements && !data.studies && !data.stats && !data.health) {
          return;
        }
        
        let html = '';
        
        if (data.health) {
          html += `
            <div class="border-l-4 border-green-500 pl-4">
              <h4 class="font-medium text-green-800">Health Status</h4>
              <p class="text-sm text-gray-600">${data.health.service} v${data.health.version}</p>
            </div>
          `;
        }
        
        if (data.stats) {
          html += `
            <div class="border-l-4 border-blue-500 pl-4">
              <h4 class="font-medium text-blue-800">Dashboard Stats</h4>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>Today's Intakes: <strong>${data.stats.today_intakes}</strong></div>
                <div>Active Supplements: <strong>${data.stats.active_supplements}</strong></div>
                <div>Current Streak: <strong>${data.stats.current_streak}</strong></div>
                <div>Weekly Compliance: <strong>${data.stats.weekly_compliance}%</strong></div>
              </div>
            </div>
          `;
        }
        
        if (data.supplements && data.supplements.length > 0) {
          html += `
            <div class="border-l-4 border-purple-500 pl-4">
              <h4 class="font-medium text-purple-800">Supplements (${data.supplements.length})</h4>
              <div class="text-sm space-y-1">
                ${data.supplements.slice(0, 3).map(s => 
                  `<div>${s.name} - ${s.brand} (${s.category})</div>`
                ).join('')}
                ${data.supplements.length > 3 ? `<div class="text-gray-500">...and ${data.supplements.length - 3} more</div>` : ''}
              </div>
            </div>
          `;
        }
        
        if (data.studies && data.studies.length > 0) {
          html += `
            <div class="border-l-4 border-orange-500 pl-4">
              <h4 class="font-medium text-orange-800">Research Studies (${data.studies.length})</h4>
              <div class="text-sm space-y-1">
                ${data.studies.slice(0, 2).map(s => 
                  `<div>${s.title} - ${s.current_participants}/${s.max_participants} participants</div>`
                ).join('')}
                ${data.studies.length > 2 ? `<div class="text-gray-500">...and ${data.studies.length - 2} more</div>` : ''}
              </div>
            </div>
          `;
        }
        
        preview.innerHTML = html;
      }
      
      async function runAllTests() {
        const button = document.getElementById('runAllTests');
        const spinner = document.getElementById('testSpinner');
        
        button.disabled = true;
        spinner.classList.remove('hidden');
        
        // Clear previous results
        document.getElementById('testLog').innerHTML = '<div class="text-gray-500">Starting tests...</div>';
        
        const results = {};
        
        // Run tests
        results.health = await testEndpoint('Health Check', `${API_BASE}/health/`, 'healthTest');
        results.supplements = await testEndpoint('Supplements Catalog', `${API_BASE}/api/v1/supplements/catalog`, 'supplementsTest');
        results.stats = await testEndpoint('Dashboard Stats', `${API_BASE}/api/v1/supplements/dashboard/stats`, 'dashboardTest');
        results.studies = await testEndpoint('Research Studies', `${API_BASE}/api/v1/research/studies`, 'researchTest');
        
        // Display results
        displayData(results);
        
        // Summary
        const passed = Object.values(results).filter(r => r !== null).length;
        const total = Object.keys(results).length;
        
        if (passed === total) {
          log(`🎉 All tests passed! (${passed}/${total})`, 'success');
        } else {
          log(`⚠️ ${passed}/${total} tests passed`, 'error');
        }
        
        button.disabled = false;
        spinner.classList.add('hidden');
      }
      
      // Auto-run tests on page load
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAllTests, 1000);
      });
      
      // Manual test button
      document.getElementById('runAllTests').addEventListener('click', runAllTests);
    </script>
  </body>
</html>
