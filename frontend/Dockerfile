# Simple nginx server for pre-built files
FROM nginx:alpine

# Copy pre-built files from host
COPY build /usr/share/nginx/html

# Copy demo files from public directory
COPY public/*.html /usr/share/nginx/html/

# Copy custom nginx configuration
COPY frontend-nginx.conf /etc/nginx/conf.d/default.conf

# Install curl for health checks
RUN apk add --no-cache curl

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80 || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
