#!/bin/bash

# Service URL Manager for Supplement Tracker
# Registers services with host-level Traefik for portless *.pills.localhost access

set -e

# Configuration
PROJECT_NAME="supplement-tracker"
TRAEFIK_NETWORK="traefik-network"
HOST_TRAEFIK_CONTAINER="traefik-controller"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if host Trae<PERSON><PERSON> is running
check_host_traefik() {
    if ! docker ps --filter "name=${HOST_TRAEFIK_CONTAINER}" --format "{{.Names}}" | grep -q "${HOST_TRAEFIK_CONTAINER}"; then
        log_error "Host Traefik container '${HOST_TRAEFIK_CONTAINER}' is not running"
        log_info "Please start the host Traefik instance first"
        exit 1
    fi
    log_success "Host Traefik container is running"
}

# Check if Traefik network exists
check_traefik_network() {
    if ! docker network ls --filter "name=${TRAEFIK_NETWORK}" --format "{{.Name}}" | grep -q "${TRAEFIK_NETWORK}"; then
        log_warning "Traefik network '${TRAEFIK_NETWORK}' does not exist, creating it..."
        docker network create ${TRAEFIK_NETWORK} || {
            log_error "Failed to create Traefik network"
            exit 1
        }
        log_success "Created Traefik network '${TRAEFIK_NETWORK}'"
    else
        log_success "Traefik network '${TRAEFIK_NETWORK}' exists"
    fi
}

# Connect host Traefik to our network
connect_traefik_to_network() {
    local supplement_network="day2-supplementtracker_supplement-network"
    
    # Check if the supplement network exists
    if docker network ls --filter "name=${supplement_network}" --format "{{.Name}}" | grep -q "${supplement_network}"; then
        # Check if Traefik is already connected to our network
        if ! docker network inspect ${supplement_network} --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "${HOST_TRAEFIK_CONTAINER}"; then
            log_info "Connecting host Traefik to supplement network..."
            docker network connect ${supplement_network} ${HOST_TRAEFIK_CONTAINER} || {
                log_warning "Failed to connect Traefik to supplement network (might already be connected)"
            }
            log_success "Connected host Traefik to supplement network"
        else
            log_success "Host Traefik already connected to supplement network"
        fi
    else
        log_warning "Supplement network does not exist yet"
    fi
}

# Register service with host Traefik
register_service() {
    local service_name=$1
    local container_name=$2
    local domain=$3
    local port=$4

    log_info "Registering service: ${service_name}"

    # Check if container exists and is running
    if ! docker ps --filter "name=${container_name}" --format "{{.Names}}" | grep -q "${container_name}"; then
        log_error "Container ${container_name} is not running"
        return 1
    fi

    # Connect container to Traefik network if not already connected
    if ! docker network inspect ${TRAEFIK_NETWORK} --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "${container_name}"; then
        log_info "Connecting ${container_name} to Traefik network..."
        docker network connect ${TRAEFIK_NETWORK} ${container_name} || {
            log_warning "Failed to connect ${container_name} to Traefik network (might already be connected)"
        }
    fi

    log_success "Registered ${service_name} at http://${domain}"
}

# Unregister service from host Traefik
unregister_service() {
    local container_name=$1

    log_info "Unregistering service: ${container_name}"

    # Disconnect from Traefik network
    if docker network inspect ${TRAEFIK_NETWORK} --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "${container_name}"; then
        docker network disconnect ${TRAEFIK_NETWORK} ${container_name} 2>/dev/null || {
            log_warning "Failed to disconnect ${container_name} from Traefik network"
        }
    fi

    log_success "Unregistered ${container_name}"
}

# Register all Supplement Tracker services
register_all() {
    log_info "Registering all Supplement Tracker services with host Traefik..."
    
    check_host_traefik
    check_traefik_network
    connect_traefik_to_network
    
    # Wait a moment for network connection to stabilize
    sleep 2
    
    # Register frontend
    if docker ps --filter "name=supplement-frontend" --format "{{.Names}}" | grep -q "supplement-frontend"; then
        register_service "supplement-frontend" "supplement-frontend" "app.pills.localhost" "80"
    else
        log_warning "Frontend container not running"
    fi
    
    # Register backend
    if docker ps --filter "name=supplement-backend" --format "{{.Names}}" | grep -q "supplement-backend"; then
        register_service "supplement-backend" "supplement-backend" "api.pills.localhost" "8000"
    else
        log_warning "Backend container not running"
    fi
    
    # Register Traefik dashboard
    if docker ps --filter "name=supplement-traefik" --format "{{.Names}}" | grep -q "supplement-traefik"; then
        register_service "supplement-traefik-dashboard" "supplement-traefik" "traefik.pills.localhost" "8080"
    else
        log_warning "Supplement Traefik container not running"
    fi
    
    log_success "Service registration complete!"
    log_info "Services available at:"
    log_info "  • Frontend: http://app.pills.localhost"
    log_info "  • Backend API: http://api.pills.localhost"
    log_info "  • Traefik Dashboard: http://traefik.pills.localhost"
}

# Unregister all services
unregister_all() {
    log_info "Unregistering all Supplement Tracker services..."
    
    unregister_service "supplement-frontend"
    unregister_service "supplement-backend"
    unregister_service "supplement-traefik"
    
    log_success "All services unregistered"
}

# Show service status
show_status() {
    log_info "Supplement Tracker Service Status:"
    echo
    
    # Check host Traefik
    if docker ps --filter "name=${HOST_TRAEFIK_CONTAINER}" --format "{{.Names}}" | grep -q "${HOST_TRAEFIK_CONTAINER}"; then
        log_success "Host Traefik: Running"
    else
        log_error "Host Traefik: Not Running"
    fi
    
    # Check our services
    for service in "supplement-frontend" "supplement-backend" "supplement-traefik"; do
        if docker ps --filter "name=${service}" --format "{{.Names}}" | grep -q "${service}"; then
            # Check if it has Traefik labels
            if docker inspect ${service} --format '{{index .Config.Labels "traefik.enable"}}' 2>/dev/null | grep -q "true"; then
                log_success "${service}: Running & Registered"
            else
                log_warning "${service}: Running but Not Registered"
            fi
        else
            log_error "${service}: Not Running"
        fi
    done
    
    echo
    log_info "Available URLs (if registered):"
    log_info "  • http://app.pills.localhost"
    log_info "  • http://api.pills.localhost"
    log_info "  • http://traefik.pills.localhost"
}

# Test connectivity
test_connectivity() {
    log_info "Testing connectivity to registered services..."
    
    local services=(
        "app.pills.localhost:Frontend"
        "api.pills.localhost/health/:Backend API"
        "traefik.pills.localhost:Traefik Dashboard"
    )
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r url name <<< "$service_info"
        
        if curl -s --max-time 5 "http://${url}" > /dev/null 2>&1; then
            log_success "${name}: Accessible at http://${url}"
        else
            log_error "${name}: Not accessible at http://${url}"
        fi
    done
}

# Main script logic
case "${1:-}" in
    "register"|"start"|"up")
        register_all
        ;;
    "unregister"|"stop"|"down")
        unregister_all
        ;;
    "status"|"ps")
        show_status
        ;;
    "test"|"check")
        test_connectivity
        ;;
    "help"|"--help"|"-h")
        echo "Supplement Tracker Service URL Manager"
        echo
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  register, start, up    Register all services with host Traefik"
        echo "  unregister, stop, down Unregister all services from host Traefik"
        echo "  status, ps             Show service registration status"
        echo "  test, check            Test connectivity to registered services"
        echo "  help                   Show this help message"
        echo
        echo "After registration, services will be available at:"
        echo "  • http://app.pills.localhost (Frontend)"
        echo "  • http://api.pills.localhost (Backend API)"
        echo "  • http://traefik.pills.localhost (Traefik Dashboard)"
        ;;
    *)
        log_error "Unknown command: ${1:-}"
        log_info "Use '$0 help' for usage information"
        exit 1
        ;;
esac
