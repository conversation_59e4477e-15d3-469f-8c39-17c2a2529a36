#!/bin/bash

# Setup /etc/hosts entries for pills.localhost domains
# This script needs to be run with sudo privileges

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Hosts entries to add
HOSTS_ENTRIES=(
    "127.0.0.1 app.pills.localhost"
    "127.0.0.1 api.pills.localhost"
    "127.0.0.1 traefik.pills.localhost"
)

# Backup file
HOSTS_BACKUP="/etc/hosts.backup.$(date +%Y%m%d_%H%M%S)"

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        log_info "Usage: sudo $0 [add|remove|check]"
        exit 1
    fi
}

# Backup hosts file
backup_hosts() {
    log_info "Creating backup of /etc/hosts..."
    cp /etc/hosts "$HOSTS_BACKUP"
    log_success "Backup created: $HOSTS_BACKUP"
}

# Add hosts entries
add_hosts() {
    log_info "Adding pills.localhost entries to /etc/hosts..."
    
    # Check if entries already exist
    local entries_exist=false
    for entry in "${HOSTS_ENTRIES[@]}"; do
        if grep -q "$(echo "$entry" | cut -d' ' -f2)" /etc/hosts; then
            log_warning "Entry already exists: $entry"
            entries_exist=true
        fi
    done
    
    if [ "$entries_exist" = true ]; then
        log_warning "Some entries already exist. Use 'remove' first if you want to replace them."
        return 1
    fi
    
    # Backup before making changes
    backup_hosts
    
    # Add entries
    echo "" >> /etc/hosts
    echo "# Supplement Tracker - pills.localhost domains" >> /etc/hosts
    for entry in "${HOSTS_ENTRIES[@]}"; do
        echo "$entry" >> /etc/hosts
        log_success "Added: $entry"
    done
    
    log_success "All pills.localhost entries added successfully!"
    log_info "You can now access:"
    log_info "  • http://app.pills.localhost"
    log_info "  • http://api.pills.localhost"
    log_info "  • http://traefik.pills.localhost"
}

# Remove hosts entries
remove_hosts() {
    log_info "Removing pills.localhost entries from /etc/hosts..."
    
    # Backup before making changes
    backup_hosts
    
    # Remove entries
    local temp_file=$(mktemp)
    grep -v "pills.localhost" /etc/hosts > "$temp_file"
    grep -v "# Supplement Tracker - pills.localhost domains" "$temp_file" > /etc/hosts
    rm "$temp_file"
    
    log_success "All pills.localhost entries removed successfully!"
}

# Check current hosts entries
check_hosts() {
    log_info "Current pills.localhost entries in /etc/hosts:"
    echo
    
    if grep -q "pills.localhost" /etc/hosts; then
        grep "pills.localhost" /etc/hosts | while read -r line; do
            log_success "Found: $line"
        done
    else
        log_warning "No pills.localhost entries found in /etc/hosts"
        echo
        log_info "To add entries, run: sudo $0 add"
    fi
    
    echo
    log_info "Testing DNS resolution:"
    for domain in "app.pills.localhost" "api.pills.localhost" "traefik.pills.localhost"; do
        if getent hosts "$domain" >/dev/null 2>&1; then
            log_success "$domain resolves correctly"
        else
            log_error "$domain does not resolve"
        fi
    done
}

# Show help
show_help() {
    echo "Setup /etc/hosts entries for pills.localhost domains"
    echo
    echo "Usage: sudo $0 [command]"
    echo
    echo "Commands:"
    echo "  add     Add pills.localhost entries to /etc/hosts"
    echo "  remove  Remove pills.localhost entries from /etc/hosts"
    echo "  check   Check current pills.localhost entries"
    echo "  help    Show this help message"
    echo
    echo "Domains that will be added:"
    for entry in "${HOSTS_ENTRIES[@]}"; do
        echo "  • $(echo "$entry" | cut -d' ' -f2)"
    done
    echo
    echo "Note: This script creates a backup of /etc/hosts before making changes"
}

# Main script logic
case "${1:-}" in
    "add")
        check_root
        add_hosts
        ;;
    "remove")
        check_root
        remove_hosts
        ;;
    "check")
        check_hosts
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        if [[ $EUID -eq 0 ]]; then
            log_error "Unknown command: ${1:-}"
            log_info "Use '$0 help' for usage information"
        else
            log_error "This script must be run as root"
            log_info "Usage: sudo $0 [add|remove|check|help]"
        fi
        exit 1
        ;;
esac
