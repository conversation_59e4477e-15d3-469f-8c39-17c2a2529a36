/**
 * Global Styles
 * 
 * Global CSS styles using styled-components.
 */

import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  /* CSS Reset and Base Styles */
  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    font-family: ${props => props.theme.typography.fontFamily.primary};
    font-size: ${props => props.theme.typography.fontSize.base};
    font-weight: ${props => props.theme.typography.fontWeight.normal};
    line-height: ${props => props.theme.typography.lineHeight.normal};
    color: ${props => props.theme.colors.text};
    background-color: ${props => props.theme.colors.background};
    transition: background-color ${props => props.theme.transitions.normal};
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
    line-height: ${props => props.theme.typography.lineHeight.tight};
    color: ${props => props.theme.colors.text};
    margin-bottom: 0.5em;
  }

  h1 {
    font-size: ${props => props.theme.typography.fontSize['3xl']};
  }

  h2 {
    font-size: ${props => props.theme.typography.fontSize['2xl']};
  }

  h3 {
    font-size: ${props => props.theme.typography.fontSize.xl};
  }

  h4 {
    font-size: ${props => props.theme.typography.fontSize.lg};
  }

  h5 {
    font-size: ${props => props.theme.typography.fontSize.base};
  }

  h6 {
    font-size: ${props => props.theme.typography.fontSize.sm};
  }

  p {
    margin-bottom: 1em;
    color: ${props => props.theme.colors.text};
  }

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    transition: color ${props => props.theme.transitions.fast};

    &:hover {
      color: ${props => props.theme.colors.primaryDark};
      text-decoration: underline;
    }

    &:focus {
      outline: 2px solid ${props => props.theme.colors.focus};
      outline-offset: 2px;
    }
  }

  /* Lists */
  ul, ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  li {
    margin-bottom: 0.25em;
  }

  /* Code */
  code {
    font-family: ${props => props.theme.typography.fontFamily.mono};
    font-size: 0.875em;
    background-color: ${props => props.theme.colors.backgroundSecondary};
    padding: 0.125em 0.25em;
    border-radius: ${props => props.theme.borderRadius.small};
    border: 1px solid ${props => props.theme.colors.border};
  }

  pre {
    font-family: ${props => props.theme.typography.fontFamily.mono};
    background-color: ${props => props.theme.colors.backgroundSecondary};
    padding: 1em;
    border-radius: ${props => props.theme.borderRadius.medium};
    border: 1px solid ${props => props.theme.colors.border};
    overflow-x: auto;
    margin-bottom: 1em;

    code {
      background: none;
      padding: 0;
      border: none;
    }
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1em;
  }

  th, td {
    padding: 0.75em;
    text-align: left;
    border-bottom: 1px solid ${props => props.theme.colors.border};
  }

  th {
    font-weight: ${props => props.theme.typography.fontWeight.semibold};
    background-color: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.text};
  }

  /* Form Elements */
  button {
    font-family: inherit;
    cursor: pointer;
  }

  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
  }

  /* Focus styles */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid ${props => props.theme.colors.focus};
    outline-offset: 2px;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.backgroundSecondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.border};
    border-radius: ${props => props.theme.borderRadius.full};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.textTertiary};
  }

  /* Selection styles */
  ::selection {
    background-color: ${props => props.theme.colors.primary}30;
    color: ${props => props.theme.colors.text};
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .font-bold {
    font-weight: ${props => props.theme.typography.fontWeight.bold};
  }

  .font-medium {
    font-weight: ${props => props.theme.typography.fontWeight.medium};
  }

  .text-primary {
    color: ${props => props.theme.colors.primary};
  }

  .text-secondary {
    color: ${props => props.theme.colors.textSecondary};
  }

  .text-error {
    color: ${props => props.theme.colors.error};
  }

  .text-success {
    color: ${props => props.theme.colors.success};
  }

  .text-warning {
    color: ${props => props.theme.colors.warning};
  }

  /* Responsive utilities */
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    .hidden-mobile {
      display: none !important;
    }
  }

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    .hidden-tablet {
      display: none !important;
    }
  }

  @media (min-width: ${props => props.theme.breakpoints.desktop}) {
    .hidden-desktop {
      display: none !important;
    }
  }

  /* Animation utilities */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-in-up {
    animation: slideInUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Print styles */
  @media print {
    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a, a:visited {
      text-decoration: underline;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    abbr[title]:after {
      content: " (" attr(title) ")";
    }

    pre, blockquote {
      border: 1px solid #999;
      page-break-inside: avoid;
    }

    thead {
      display: table-header-group;
    }

    tr, img {
      page-break-inside: avoid;
    }

    img {
      max-width: 100% !important;
    }

    p, h2, h3 {
      orphans: 3;
      widows: 3;
    }

    h2, h3 {
      page-break-after: avoid;
    }
  }
`;

export default GlobalStyles;
