{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "copy": "Copy", "share": "Share", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "home": "Home", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "all": "All", "none": "None", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "week": "Week", "month": "Month", "year": "Year", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "password": "Password", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "auto": "Auto"}, "navigation": {"dashboard": "Dashboard", "supplements": "Supplements", "tracking": "Tracking", "research": "Research", "community": "Community", "analytics": "Analytics", "health": "Health Metrics", "profile": "Profile", "settings": "Settings"}, "auth": {"login": {"title": "Sign In", "subtitle": "Welcome back! Please sign in to your account.", "email": "Email Address", "password": "Password", "remember": "Remember me", "forgot": "Forgot password?", "submit": "Sign In", "noAccount": "Don't have an account?", "signUp": "Sign up here"}, "register": {"title": "Create Account", "subtitle": "Join our community and start tracking your supplements.", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "terms": "I agree to the Terms of Service and Privacy Policy", "submit": "Create Account", "hasAccount": "Already have an account?", "signIn": "Sign in here"}, "forgotPassword": {"title": "Reset Password", "subtitle": "Enter your email to receive a password reset link.", "email": "Email Address", "submit": "Send Reset Link", "back": "Back to Sign In"}, "errors": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "emailInvalid": "Please enter a valid email address", "termsRequired": "You must agree to the terms and conditions"}}, "supplements": {"title": "My Supplements", "add": "Add Supplement", "search": "Search supplements...", "filter": {"all": "All Supplements", "active": "Active", "inactive": "Inactive", "category": "Category", "brand": "Brand"}, "sort": {"name": "Name", "dateAdded": "Date Added", "lastTaken": "Last Taken", "frequency": "Frequency"}, "form": {"name": "Supplement Name", "brand": "Brand", "category": "Category", "dosage": "Dosage", "unit": "Unit", "frequency": "Frequency", "timeOfDay": "Time of Day", "withFood": "Take with food", "notes": "Notes", "startDate": "Start Date", "endDate": "End Date", "reminderEnabled": "Enable reminders", "reminderTime": "Reminder Time"}, "categories": {"vitamins": "Vitamins", "minerals": "Minerals", "herbs": "<PERSON><PERSON>", "probiotics": "Probiotics", "omega3": "Omega-3", "protein": "<PERSON><PERSON>", "other": "Other"}, "frequency": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "asNeeded": "As Needed"}, "timeOfDay": {"morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "bedtime": "Bedtime"}, "status": {"active": "Active", "inactive": "Inactive", "completed": "Completed"}, "actions": {"take": "Take Now", "skip": "<PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "archive": "Archive"}, "messages": {"added": "Supplement added successfully", "updated": "Supplement updated successfully", "deleted": "Supplement deleted successfully", "taken": "Supplement intake recorded", "skipped": "Supplement intake skipped"}}, "tracking": {"title": "Supplement Tracking", "today": "Today's Schedule", "upcoming": "Upcoming", "history": "History", "adherence": "Adherence Rate", "streak": "Current Streak", "missed": "<PERSON>ed <PERSON><PERSON>", "calendar": "Calendar View", "log": {"title": "Log Intake", "supplement": "Supplement", "dosage": "Dosage", "time": "Time Taken", "notes": "Notes", "sideEffects": "Side Effects", "effectiveness": "Effectiveness"}, "effectiveness": {"veryPoor": "Very Poor", "poor": "Poor", "fair": "Fair", "good": "Good", "excellent": "Excellent"}, "messages": {"intakeLogged": "Intake logged successfully", "intakeUpdated": "Intake updated successfully", "intakeDeleted": "Intake deleted successfully"}}, "research": {"title": "Research Database", "search": "Search research...", "filter": {"all": "All Studies", "supplement": "By Supplement", "condition": "By Condition", "studyType": "Study Type", "quality": "Study Quality"}, "studyTypes": {"rct": "Randomized Controlled Trial", "cohort": "Cohort Study", "caseControl": "Case-Control Study", "crossSectional": "Cross-Sectional Study", "metaAnalysis": "Meta-Analysis", "systematicReview": "Systematic Review"}, "quality": {"high": "High Quality", "moderate": "Moderate Quality", "low": "Low Quality"}, "details": {"abstract": "Abstract", "methodology": "Methodology", "results": "Results", "conclusion": "Conclusion", "participants": "Participants", "duration": "Duration", "dosage": "Dosage", "sideEffects": "Side Effects", "limitations": "Limitations", "funding": "Funding Source"}}, "community": {"title": "Community", "feed": "Feed", "posts": "Posts", "discussions": "Discussions", "groups": "Groups", "experts": "Experts", "createPost": "Create Post", "postTypes": {"question": "Question", "experience": "Experience", "review": "Review", "research": "Research", "general": "General"}, "actions": {"like": "Like", "comment": "Comment", "share": "Share", "follow": "Follow", "unfollow": "Unfollow", "report": "Report"}, "form": {"title": "Post Title", "content": "Content", "tags": "Tags", "category": "Category", "anonymous": "Post anonymously"}, "messages": {"postCreated": "Post created successfully", "postUpdated": "Post updated successfully", "postDeleted": "Post deleted successfully", "commentAdded": "Comment added successfully", "liked": "Post liked", "unliked": "Post unliked", "followed": "User followed", "unfollowed": "User unfollowed"}}, "analytics": {"title": "Analytics Dashboard", "overview": "Overview", "trends": "Trends", "insights": {"title": "AI-Powered Insights", "adherenceImprovement": "Your adherence has improved by {{percentage}}% this month", "consistentSupplement": "{{supplement}} is your most consistently taken supplement", "missedDosePattern": "You tend to miss doses on {{day}}s", "effectivenessCorrelation": "{{supplement}} shows highest effectiveness when taken {{time}}"}, "reports": "Reports", "metrics": {"totalSupplements": "Total Supplements", "adherenceRate": "Adherence Rate", "totalIntakes": "Total Intakes", "currentStreak": "Current Streak", "missedDoses": "<PERSON>ed <PERSON><PERSON>", "averageEffectiveness": "Average Effectiveness"}, "charts": {"adherenceTrend": "Adherence Trend", "supplementDistribution": "Supplement Distribution", "categoryBreakdown": "Category Breakdown", "effectivenessOverTime": "Effectiveness Over Time", "sideEffectsFrequency": "Side Effects Frequency"}, "timeRanges": {"7days": "Last 7 days", "30days": "Last 30 days", "3months": "Last 3 months", "6months": "Last 6 months", "1year": "Last year", "custom": "Custom range"}, "export": {"title": "Export Data", "format": "Format", "dateRange": "Date Range", "includeCharts": "Include Charts", "download": "Download Report"}}, "health": {"title": "Health Metrics", "overview": "Overview", "metrics": {"heartRate": "Heart Rate", "bloodPressure": "Blood Pressure", "weight": "Weight", "bodyFat": "Body Fat", "sleepHours": "Sleep Hours", "sleepQuality": "Sleep Quality", "steps": "Steps", "calories": "Calories Burned", "mood": "<PERSON><PERSON>", "energy": "Energy Level", "stress": "Stress Level"}, "correlations": {"title": "Health-Supplement Correlations", "strength": "Correlation Strength", "confidence": "Confidence", "positive": "Positive correlation", "negative": "Negative correlation", "noCorrelation": "No significant correlation"}, "devices": {"fitbit": "Fitbit", "appleHealth": "Apple Health", "googleFit": "Google Fit", "garmin": "<PERSON><PERSON><PERSON>", "samsungHealth": "Samsung Health", "connect": "Connect", "disconnect": "Disconnect", "sync": "Sync Now", "lastSync": "Last sync", "syncFrequency": "Sync frequency", "connected": "Connected", "notConnected": "Not Connected"}, "addMetric": "Add Metric", "categories": {"vitals": "Vital Signs", "fitness": "Fitness", "sleep": "Sleep", "nutrition": "Nutrition", "mentalHealth": "Mental Health", "biomarkers": "Biomarkers"}, "units": {"bpm": "bpm", "mmHg": "mmHg", "kg": "kg", "lbs": "lbs", "percent": "%", "hours": "hours", "steps": "steps", "calories": "cal", "scale1to10": "1-10 scale"}, "status": {"optimal": "Optimal", "warning": "Warning", "critical": "Critical", "unknown": "Unknown"}, "form": {"metricType": "Metric Type", "value": "Value", "unit": "Unit", "date": "Date", "time": "Time", "notes": "Notes", "targetRange": "Target Range", "min": "Min", "max": "Max"}, "messages": {"metricAdded": "Health metric added successfully", "metricUpdated": "Health metric updated successfully", "metricDeleted": "Health metric deleted successfully", "deviceConnected": "<PERSON>ce connected successfully", "deviceDisconnected": "<PERSON><PERSON> disconnected successfully", "syncCompleted": "Data sync completed successfully", "syncFailed": "Data sync failed"}}, "profile": {"title": "Profile", "personal": "Personal Information", "preferences": "Preferences", "privacy": {"profileVisibility": "Profile Visibility", "dataSharing": "Data Sharing", "analytics": "Analytics Tracking", "public": "Public", "private": "Private", "friendsOnly": "Friends Only"}, "notifications": {"email": "Email Notifications", "push": "Push Notifications", "reminders": "Supplement Reminders", "research": "Research Updates", "community": "Community Activity", "marketing": "Marketing Communications"}, "subscription": "Subscription", "form": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "dateOfBirth": "Date of Birth", "gender": "Gender", "height": "Height", "weight": "Weight", "timezone": "Timezone", "language": "Language", "units": "Units", "theme": "Theme"}, "gender": {"male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}, "units": {"metric": "Metric", "imperial": "Imperial"}, "messages": {"updated": "Profile updated successfully", "passwordChanged": "Password changed successfully", "emailVerified": "Email verified successfully", "phoneVerified": "Phone verified successfully"}}, "settings": {"title": "Settings", "general": "General", "account": {"changePassword": "Change Password", "twoFactor": "Two-Factor Authentication", "connectedAccounts": "Connected Accounts", "deleteAccount": "Delete Account"}, "privacy": "Privacy", "notifications": "Notifications", "data": {"export": "Export Data", "import": "Import Data", "backup": "Backup", "restore": "Rest<PERSON>", "clear": "Clear All Data"}, "support": {"helpCenter": "Help Center", "contactSupport": "Contact Support", "reportBug": "Report Bug", "featureRequest": "Feature Request", "documentation": "Documentation"}, "about": "About", "generalSettings": {"language": "Language", "theme": "Theme", "timezone": "Timezone", "units": "Units", "currency": "<PERSON><PERSON><PERSON><PERSON>"}}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again.", "server": "Server error. Please try again later.", "timeout": "Request timed out. Please try again.", "offline": "You are currently offline.", "maintenance": "The service is currently under maintenance."}, "success": {"saved": "Changes saved successfully", "deleted": "Item deleted successfully", "created": "Item created successfully", "updated": "Item updated successfully", "sent": "Message sent successfully", "uploaded": "File uploaded successfully", "downloaded": "File downloaded successfully", "copied": "Copied to clipboard", "shared": "Shared successfully"}}