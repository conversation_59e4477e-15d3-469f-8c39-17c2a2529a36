import { useNotificationStore, notify } from '@/stores/notification-store'

export const useToast = () => {
  const { addNotification, removeNotification, clearAllNotifications } = useNotificationStore()

  const toast = {
    success: (message: string, title?: string) => {
      notify.success(message, title)
    },
    error: (message: string, title?: string) => {
      notify.error(message, title)
    },
    warning: (message: string, title?: string) => {
      notify.warning(message, title)
    },
    info: (message: string, title?: string) => {
      notify.info(message, title)
    },
    custom: (notification: Parameters<typeof addNotification>[0]) => {
      addNotification(notification)
    },
    dismiss: (id: string) => {
      removeNotification(id)
    },
    dismissAll: () => {
      clearAllNotifications()
    },
  }

  return { toast }
}

export default useToast
