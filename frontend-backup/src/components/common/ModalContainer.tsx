/**
 * Modal Container Component
 * 
 * Displays global modals from the Redux store.
 */

import React, { useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { useAppSelector, useAppDispatch } from '@/store';
import { selectModals, closeModal } from '@/store/slices/uiSlice';

// Animations
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const slideIn = keyframes`
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
`;

// Styled components
const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: ${props => props.theme.zIndex.modal};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  animation: ${fadeIn} 0.2s ease;
`;

const ModalContent = styled.div<{ $size: string }>`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: ${slideIn} 0.3s ease;
  max-height: 90vh;
  overflow-y: auto;
  
  ${props => {
    switch (props.$size) {
      case 'small':
        return `
          width: 100%;
          max-width: 400px;
        `;
      case 'large':
        return `
          width: 100%;
          max-width: 800px;
        `;
      case 'fullscreen':
        return `
          width: 95vw;
          height: 95vh;
          max-width: none;
          max-height: none;
        `;
      default: // medium
        return `
          width: 100%;
          max-width: 600px;
        `;
    }
  }}
`;

// Modal component registry (placeholder)
const modalComponents: Record<string, React.ComponentType<any>> = {
  // Add modal components here as they're created
  // 'confirm': ConfirmModal,
  // 'supplement-form': SupplementFormModal,
  // etc.
};

// Individual modal component
interface ModalItemProps {
  id: string;
  component: string;
  props?: Record<string, any>;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  onClose: (id: string) => void;
}

const ModalItem: React.FC<ModalItemProps> = ({
  id,
  component,
  props = {},
  size = 'medium',
  onClose,
}) => {
  const ModalComponent = modalComponents[component];

  useEffect(() => {
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  useEffect(() => {
    // Close modal on Escape key
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose(id);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [id, onClose]);

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose(id);
    }
  };

  if (!ModalComponent) {
    console.warn(`Modal component "${component}" not found`);
    return null;
  }

  return (
    <Overlay onClick={handleOverlayClick}>
      <ModalContent $size={size}>
        <ModalComponent
          {...props}
          onClose={() => onClose(id)}
        />
      </ModalContent>
    </Overlay>
  );
};

// Main modal container
const ModalContainer: React.FC = () => {
  const modals = useAppSelector(selectModals);
  const dispatch = useAppDispatch();

  const handleClose = (id: string) => {
    dispatch(closeModal(id));
  };

  if (modals.length === 0) {
    return null;
  }

  // Render only the top modal (latest)
  const topModal = modals[modals.length - 1];

  return (
    <ModalItem
      key={topModal.id}
      {...topModal}
      onClose={handleClose}
    />
  );
};

export default ModalContainer;
