/**
 * Card Component
 * 
 * Reusable card component for content containers.
 */

import React from 'react';
import styled, { css } from 'styled-components';

interface CardProps {
  children: React.ReactNode;
  padding?: 'none' | 'small' | 'medium' | 'large';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  border?: boolean;
  hover?: boolean;
  className?: string;
  onClick?: () => void;
}

const StyledCard = styled.div<{
  $padding: CardProps['padding'];
  $shadow: CardProps['shadow'];
  $border: boolean;
  $hover: boolean;
  $clickable: boolean;
}>`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  transition: all 0.2s ease;
  
  ${props => props.$border && css`
    border: 1px solid ${props.theme.colors.border};
  `}
  
  ${props => props.$clickable && css`
    cursor: pointer;
  `}
  
  /* Padding variants */
  ${props => {
    switch (props.$padding) {
      case 'none':
        return css`padding: 0;`;
      case 'small':
        return css`padding: 1rem;`;
      case 'large':
        return css`padding: 2rem;`;
      default: // medium
        return css`padding: 1.5rem;`;
    }
  }}
  
  /* Shadow variants */
  ${props => {
    switch (props.$shadow) {
      case 'none':
        return css`box-shadow: none;`;
      case 'small':
        return css`box-shadow: 0 1px 3px ${props.theme.colors.shadow}20;`;
      case 'large':
        return css`box-shadow: 0 10px 25px ${props.theme.colors.shadow}30;`;
      default: // medium
        return css`box-shadow: 0 4px 12px ${props.theme.colors.shadow}25;`;
    }
  }}
  
  /* Hover effects */
  ${props => props.$hover && css`
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px ${props.theme.colors.shadow}30;
    }
  `}
`;

const Card: React.FC<CardProps> = ({
  children,
  padding = 'medium',
  shadow = 'medium',
  border = false,
  hover = false,
  className,
  onClick,
}) => {
  return (
    <StyledCard
      $padding={padding}
      $shadow={shadow}
      $border={border}
      $hover={hover}
      $clickable={!!onClick}
      className={className}
      onClick={onClick}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
