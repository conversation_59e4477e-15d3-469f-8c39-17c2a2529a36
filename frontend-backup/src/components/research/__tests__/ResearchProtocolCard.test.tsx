/**
 * ResearchProtocolCard Component Tests
 * 
 * Test-driven development tests for the ResearchProtocolCard component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON>Router } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { configureStore } from '@reduxjs/toolkit';

// Components and store
import ResearchProtocolCard from '../ResearchProtocolCard';
import authReducer from '@/store/slices/authSlice';
import researchReducer from '@/store/slices/researchSlice';
import uiReducer from '@/store/slices/uiSlice';
import { lightTheme } from '@/styles/theme';

// Test data
const mockProtocol = {
  id: '1',
  title: 'Vitamin D3 Efficacy Study',
  description: 'A comprehensive study examining the effects of Vitamin D3 supplementation on immune function and bone health.',
  status: 'recruiting' as const,
  duration_weeks: 12,
  max_participants: 100,
  participant_count: 45,
  objectives: [
    'Measure immune function markers',
    'Assess bone density changes',
    'Monitor vitamin D blood levels'
  ],
  inclusion_criteria: [
    'Age 18-65',
    'Vitamin D deficiency',
    'No current supplementation'
  ],
  exclusion_criteria: [
    'Pregnancy',
    'Kidney disease',
    'Current vitamin D supplementation'
  ],
  primary_endpoints: [
    'Serum 25(OH)D levels',
    'Immune function markers'
  ],
  secondary_endpoints: [
    'Bone density',
    'Quality of life scores'
  ],
  supplements: ['Vitamin D3'],
  data_collection_schedule: 'Weekly surveys and monthly blood tests',
  ethical_considerations: 'IRB approved protocol with informed consent',
  consent_form_url: 'https://example.com/consent.pdf',
  created_by_user: {
    id: '1',
    email: '<EMAIL>',
    full_name: 'Dr. Jane Smith',
    is_active: true,
    is_superuser: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
      research: researchReducer,
      ui: uiReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: '2',
          email: '<EMAIL>',
          full_name: 'Test User',
          is_active: true,
          is_superuser: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      research: {
        protocols: [],
        currentProtocol: null,
        myProtocols: [],
        participants: [],
        literature: [],
        myParticipations: [],
        dataCollections: [],
        isLoading: false,
        isCreatingProtocol: false,
        isJoiningStudy: false,
        isSubmittingData: false,
        error: null,
        filters: {
          status: '',
          search: '',
        },
        pagination: {
          total: 0,
          page: 1,
          size: 20,
          pages: 0,
        },
      },
      ui: {
        theme: 'light',
        sidebarOpen: true,
        notifications: [],
        modals: [],
        globalLoading: false,
        pageTitle: 'Research',
        breadcrumbs: [],
        searchQuery: '',
        mobileMenuOpen: false,
      },
      ...initialState,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={lightTheme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('ResearchProtocolCard', () => {
  const mockOnJoin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render protocol information correctly', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      expect(screen.getByText('Vitamin D3 Efficacy Study')).toBeInTheDocument();
      expect(screen.getByText('Dr. Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('recruiting')).toBeInTheDocument();
      expect(screen.getByText('12 weeks')).toBeInTheDocument();
      expect(screen.getByText('45 / 100')).toBeInTheDocument();
    });

    it('should render protocol description', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      expect(screen.getByText(/comprehensive study examining/i)).toBeInTheDocument();
    });

    it('should render protocol statistics', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      expect(screen.getByText('12')).toBeInTheDocument(); // Duration
      expect(screen.getByText('45')).toBeInTheDocument(); // Participants
      expect(screen.getByText('45%')).toBeInTheDocument(); // Participation rate
      expect(screen.getByText('3')).toBeInTheDocument(); // Objectives count
    });

    it('should render key objectives', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      expect(screen.getByText('Key Objectives')).toBeInTheDocument();
      expect(screen.getByText('Measure immune function markers')).toBeInTheDocument();
      expect(screen.getByText('Assess bone density changes')).toBeInTheDocument();
      expect(screen.getByText('Monitor vitamin D blood levels')).toBeInTheDocument();
    });

    it('should render compact mode correctly', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} compact />
        </TestWrapper>
      );

      expect(screen.getByText('Vitamin D3 Efficacy Study')).toBeInTheDocument();
      expect(screen.queryByText(/comprehensive study examining/i)).not.toBeInTheDocument();
      expect(screen.queryByText('Key Objectives')).not.toBeInTheDocument();
    });
  });

  describe('Status Display', () => {
    it('should display recruiting status correctly', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      const statusBadge = screen.getByText('recruiting');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveStyle('color: #10b981'); // Success color
    });

    it('should display active status correctly', () => {
      const activeProtocol = { ...mockProtocol, status: 'active' as const };
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={activeProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      const statusBadge = screen.getByText('active');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveStyle('color: #2563eb'); // Primary color
    });

    it('should display completed status correctly', () => {
      const completedProtocol = { ...mockProtocol, status: 'completed' as const };
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={completedProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      const statusBadge = screen.getByText('completed');
      expect(statusBadge).toBeInTheDocument();
    });
  });

  describe('Join Study Functionality', () => {
    it('should show join button for recruiting studies', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /join study/i })).toBeInTheDocument();
    });

    it('should not show join button when showJoinButton is false', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton={false} />
        </TestWrapper>
      );

      expect(screen.queryByRole('button', { name: /join study/i })).not.toBeInTheDocument();
    });

    it('should show "Study Full" when max participants reached', () => {
      const fullProtocol = { ...mockProtocol, participant_count: 100 };
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={fullProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /study full/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /study full/i })).toBeDisabled();
    });

    it('should not show join button for non-recruiting studies', () => {
      const activeProtocol = { ...mockProtocol, status: 'active' as const };
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={activeProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      expect(screen.queryByRole('button', { name: /join study/i })).not.toBeInTheDocument();
    });

    it('should call onJoin when join button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      const joinButton = screen.getByRole('button', { name: /join study/i });
      await user.click(joinButton);

      expect(mockOnJoin).toHaveBeenCalledWith('1');
    });

    it('should show loading state when joining', async () => {
      const user = userEvent.setup();
      mockOnJoin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      const joinButton = screen.getByRole('button', { name: /join study/i });
      await user.click(joinButton);

      expect(screen.getByText('Joining...')).toBeInTheDocument();
      expect(joinButton).toBeDisabled();
    });
  });

  describe('Navigation', () => {
    it('should navigate to protocol details when card is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      const card = screen.getByText('Vitamin D3 Efficacy Study').closest('div');
      await user.click(card!);

      // Would check navigation in a real test environment
      // expect(mockNavigate).toHaveBeenCalledWith('/research/protocols/1');
    });

    it('should navigate to details when details button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} />
        </TestWrapper>
      );

      const detailsButton = screen.getByRole('button', { name: /view details/i });
      await user.click(detailsButton);

      // Would check navigation in a real test environment
      // expect(mockNavigate).toHaveBeenCalledWith('/research/protocols/1');
    });

    it('should not navigate when clicking on action buttons', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      const joinButton = screen.getByRole('button', { name: /join study/i });
      await user.click(joinButton);

      // Should not navigate when clicking buttons
      expect(mockOnJoin).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles and labels', () => {
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      expect(screen.getByRole('button', { name: /join study/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /view details/i })).toBeInTheDocument();
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      // Tab to join button
      await user.tab();
      expect(screen.getByRole('button', { name: /join study/i })).toHaveFocus();

      // Tab to details button
      await user.tab();
      expect(screen.getByRole('button', { name: /view details/i })).toHaveFocus();
    });

    it('should have proper ARIA attributes for disabled states', () => {
      const fullProtocol = { ...mockProtocol, participant_count: 100 };
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={fullProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      const fullButton = screen.getByRole('button', { name: /study full/i });
      expect(fullButton).toBeDisabled();
      expect(fullButton).toHaveAttribute('disabled');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing user gracefully', () => {
      const storeWithoutUser = createTestStore({
        auth: {
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutUser}>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      // Should still render the card
      expect(screen.getByText('Vitamin D3 Efficacy Study')).toBeInTheDocument();
    });

    it('should handle join errors gracefully', async () => {
      const user = userEvent.setup();
      mockOnJoin.mockRejectedValue(new Error('Join failed'));
      
      render(
        <TestWrapper>
          <ResearchProtocolCard protocol={mockProtocol} onJoin={mockOnJoin} showJoinButton />
        </TestWrapper>
      );

      const joinButton = screen.getByRole('button', { name: /join study/i });
      await user.click(joinButton);

      // Should handle error and reset button state
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /join study/i })).not.toBeDisabled();
      });
    });
  });
});
