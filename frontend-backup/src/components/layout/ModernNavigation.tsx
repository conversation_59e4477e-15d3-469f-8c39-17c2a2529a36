import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { 
  Home, 
  Pill, 
  Activity, 
  Search, 
  Users, 
  BarChart3, 
  Heart,
  User,
  Settings,
  Menu,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import LanguageSwitcher from '@/components/ui/language-switcher'
import { useTheme } from '@/providers/theme-provider'

interface NavigationItem {
  key: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  requiresAuth?: boolean
}

const navigationItems: NavigationItem[] = [
  { key: 'dashboard', href: '/dashboard', icon: Home, requiresAuth: true },
  { key: 'supplements', href: '/supplements', icon: Pill, requiresAuth: true },
  { key: 'tracking', href: '/tracking', icon: Activity, requiresAuth: true },
  { key: 'research', href: '/research', icon: Search },
  { key: 'community', href: '/community', icon: Users },
  { key: 'analytics', href: '/analytics', icon: BarChart3, requiresAuth: true },
  { key: 'health', href: '/health', icon: Heart, requiresAuth: true },
]

const userMenuItems: NavigationItem[] = [
  { key: 'profile', href: '/profile', icon: User, requiresAuth: true },
  { key: 'settings', href: '/settings', icon: Settings, requiresAuth: true },
]

interface ModernNavigationProps {
  isAuthenticated: boolean
  onLogout: () => void
  className?: string
}

export const ModernNavigation: React.FC<ModernNavigationProps> = ({
  isAuthenticated,
  onLogout,
  className,
}) => {
  const { t } = useTranslation()
  const location = useLocation()
  const { theme, setTheme } = useTheme()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard'
    }
    return location.pathname.startsWith(href)
  }

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  const filteredNavItems = navigationItems.filter(item => 
    !item.requiresAuth || isAuthenticated
  )

  const filteredUserItems = userMenuItems.filter(item => 
    !item.requiresAuth || isAuthenticated
  )

  return (
    <nav className={cn('bg-background border-b border-border', className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and main navigation */}
          <div className="flex">
            {/* Logo */}
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Pill className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="font-bold text-xl text-foreground">
                  SupplementTracker
                </span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:ml-6 md:flex md:space-x-1">
              {filteredNavItems.map((item) => {
                const Icon = item.icon
                const isActive = isActiveRoute(item.href)
                
                return (
                  <Link
                    key={item.key}
                    to={item.href}
                    className={cn(
                      'inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                      isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                    )}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {t(`navigation.${item.key}`)}
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Right side items */}
          <div className="flex items-center space-x-4">
            {/* Language switcher */}
            <LanguageSwitcher className="hidden sm:block" />

            {/* Theme toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="hidden sm:flex"
            >
              {theme === 'light' ? '🌙' : '☀️'}
            </Button>

            {/* User menu for authenticated users */}
            {isAuthenticated && (
              <div className="hidden md:flex md:items-center md:space-x-1">
                {filteredUserItems.map((item) => {
                  const Icon = item.icon
                  const isActive = isActiveRoute(item.href)
                  
                  return (
                    <Link
                      key={item.key}
                      to={item.href}
                      className={cn(
                        'inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-primary text-primary-foreground'
                          : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                      )}
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {t(`navigation.${item.key}`)}
                    </Link>
                  )
                })}
                
                <Button
                  variant="ghost"
                  onClick={onLogout}
                  className="text-muted-foreground hover:text-foreground"
                >
                  {t('common.logout')}
                </Button>
              </div>
            )}

            {/* Auth buttons for non-authenticated users */}
            {!isAuthenticated && (
              <div className="hidden md:flex md:space-x-2">
                <Button variant="ghost" asChild>
                  <Link to="/login">{t('common.login')}</Link>
                </Button>
                <Button asChild>
                  <Link to="/register">{t('common.register')}</Link>
                </Button>
              </div>
            )}

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 bg-background border-t border-border">
            {filteredNavItems.map((item) => {
              const Icon = item.icon
              const isActive = isActiveRoute(item.href)
              
              return (
                <Link
                  key={item.key}
                  to={item.href}
                  className={cn(
                    'flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors',
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {t(`navigation.${item.key}`)}
                </Link>
              )
            })}

            {/* Mobile user menu */}
            {isAuthenticated && (
              <>
                <div className="border-t border-border pt-4 mt-4">
                  {filteredUserItems.map((item) => {
                    const Icon = item.icon
                    const isActive = isActiveRoute(item.href)
                    
                    return (
                      <Link
                        key={item.key}
                        to={item.href}
                        className={cn(
                          'flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors',
                          isActive
                            ? 'bg-primary text-primary-foreground'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                        )}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <Icon className="w-5 h-5 mr-3" />
                        {t(`navigation.${item.key}`)}
                      </Link>
                    )
                  })}
                  
                  <button
                    onClick={() => {
                      onLogout()
                      setIsMobileMenuOpen(false)
                    }}
                    className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
                  >
                    {t('common.logout')}
                  </button>
                </div>
              </>
            )}

            {/* Mobile auth buttons */}
            {!isAuthenticated && (
              <div className="border-t border-border pt-4 mt-4 space-y-2">
                <Link
                  to="/login"
                  className="flex items-center px-3 py-2 rounded-md text-base font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {t('common.login')}
                </Link>
                <Link
                  to="/register"
                  className="flex items-center px-3 py-2 rounded-md text-base font-medium bg-primary text-primary-foreground transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {t('common.register')}
                </Link>
              </div>
            )}

            {/* Mobile utilities */}
            <div className="border-t border-border pt-4 mt-4 flex items-center justify-between">
              <LanguageSwitcher variant="buttons" />
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleTheme}
              >
                {theme === 'light' ? '🌙' : '☀️'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}

export default ModernNavigation
