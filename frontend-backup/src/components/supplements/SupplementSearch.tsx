/**
 * Supplement Search Component
 * 
 * Advanced search interface for supplements with filters and real-time results.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchSupplements, 
  updateFilters, 
  selectSupplements, 
  selectSupplementsLoading, 
  selectSupplementsFilters,
  selectSupplementsPagination 
} from '@/store/slices/supplementSlice';
import styled from 'styled-components';

// Components
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import SupplementCard from './SupplementCard';

// Icons (placeholder - would use actual icon library)
const SearchIcon = () => <span>🔍</span>;
const FilterIcon = () => <span>🔽</span>;
const ClearIcon = () => <span>✕</span>;

// Styled components
const SearchContainer = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid ${props => props.theme.colors.border};
`;

const SearchHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const SearchInputWrapper = styled.div`
  flex: 1;
  position: relative;
`;

const FiltersRow = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 0.5rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9rem;
  min-width: 150px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const ResultsContainer = styled.div`
  margin-top: 2rem;
`;

const ResultsHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0;
  }
  
  span {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 0.9rem;
  }
`;

const ResultsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: ${props => props.theme.colors.textSecondary};
  
  h4 {
    margin-bottom: 0.5rem;
    color: ${props => props.theme.colors.text};
  }
  
  p {
    margin-bottom: 1.5rem;
  }
`;

const LoadMoreButton = styled(Button)`
  margin: 2rem auto 0;
  display: block;
`;

// Supplement categories
const SUPPLEMENT_CATEGORIES = [
  { value: '', label: 'All Categories' },
  { value: 'vitamins', label: 'Vitamins' },
  { value: 'minerals', label: 'Minerals' },
  { value: 'herbs', label: 'Herbs & Botanicals' },
  { value: 'amino-acids', label: 'Amino Acids' },
  { value: 'probiotics', label: 'Probiotics' },
  { value: 'omega-3', label: 'Omega-3 & Fatty Acids' },
  { value: 'protein', label: 'Protein & Fitness' },
  { value: 'specialty', label: 'Specialty Supplements' },
];

interface SupplementSearchProps {
  onSupplementSelect?: (supplementId: string) => void;
  showQuickAdd?: boolean;
  compact?: boolean;
}

const SupplementSearch: React.FC<SupplementSearchProps> = ({
  onSupplementSelect,
  showQuickAdd = false,
  compact = false,
}) => {
  const dispatch = useAppDispatch();
  const supplements = useAppSelector(selectSupplements);
  const isLoading = useAppSelector(selectSupplementsLoading);
  const filters = useAppSelector(selectSupplementsFilters);
  const pagination = useAppSelector(selectSupplementsPagination);

  const [localSearch, setLocalSearch] = useState(filters.search);
  const [showFilters, setShowFilters] = useState(!compact);
  
  // Debounce search input
  const debouncedSearch = useDebounce(localSearch, 300);

  // Update filters when debounced search changes
  useEffect(() => {
    if (debouncedSearch !== filters.search) {
      dispatch(updateFilters({ search: debouncedSearch }));
    }
  }, [debouncedSearch, filters.search, dispatch]);

  // Fetch supplements when filters change
  useEffect(() => {
    dispatch(fetchSupplements({
      search: filters.search,
      category: filters.category,
      skip: 0,
      limit: 20,
    }));
  }, [filters, dispatch]);

  const handleCategoryChange = (category: string) => {
    dispatch(updateFilters({ category }));
  };

  const handleClearFilters = () => {
    setLocalSearch('');
    dispatch(updateFilters({ search: '', category: '' }));
  };

  const handleLoadMore = () => {
    dispatch(fetchSupplements({
      search: filters.search,
      category: filters.category,
      skip: supplements.length,
      limit: 20,
    }));
  };

  const hasActiveFilters = filters.search || filters.category;
  const hasMoreResults = supplements.length < pagination.total;

  return (
    <>
      <SearchContainer>
        <SearchHeader>
          <SearchInputWrapper>
            <Input
              type="text"
              placeholder="Search supplements by name, brand, or ingredient..."
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              leftIcon={<SearchIcon />}
              rightIcon={
                localSearch ? (
                  <button 
                    onClick={() => setLocalSearch('')}
                    style={{ background: 'none', border: 'none', cursor: 'pointer' }}
                  >
                    <ClearIcon />
                  </button>
                ) : undefined
              }
              fullWidth
            />
          </SearchInputWrapper>
          
          {compact && (
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              leftIcon={<FilterIcon />}
            >
              Filters
            </Button>
          )}
        </SearchHeader>

        {showFilters && (
          <FiltersRow>
            <FilterSelect
              value={filters.category}
              onChange={(e) => handleCategoryChange(e.target.value)}
            >
              {SUPPLEMENT_CATEGORIES.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </FilterSelect>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="small"
                onClick={handleClearFilters}
                leftIcon={<ClearIcon />}
              >
                Clear Filters
              </Button>
            )}
          </FiltersRow>
        )}
      </SearchContainer>

      <ResultsContainer>
        <ResultsHeader>
          <h3>
            {filters.search || filters.category ? 'Search Results' : 'All Supplements'}
          </h3>
          <span>
            {isLoading ? 'Searching...' : `${pagination.total} supplements found`}
          </span>
        </ResultsHeader>

        {isLoading && supplements.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <LoadingSpinner size="large" />
          </div>
        ) : supplements.length === 0 ? (
          <EmptyState>
            <h4>No supplements found</h4>
            <p>
              {hasActiveFilters 
                ? 'Try adjusting your search terms or filters'
                : 'No supplements are available at the moment'
              }
            </p>
            {hasActiveFilters && (
              <Button variant="outline" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            )}
          </EmptyState>
        ) : (
          <>
            <ResultsGrid>
              {supplements.map(supplement => (
                <SupplementCard
                  key={supplement.id}
                  supplement={supplement}
                  onSelect={onSupplementSelect}
                  showQuickAdd={showQuickAdd}
                />
              ))}
            </ResultsGrid>

            {hasMoreResults && (
              <LoadMoreButton
                variant="outline"
                onClick={handleLoadMore}
                disabled={isLoading}
              >
                {isLoading ? <LoadingSpinner size="small" /> : 'Load More'}
              </LoadMoreButton>
            )}
          </>
        )}
      </ResultsContainer>
    </>
  );
};

export default SupplementSearch;
