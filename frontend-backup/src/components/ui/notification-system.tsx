import React from 'react'
import { <PERSON><PERSON>ir<PERSON>, XCircle, AlertTriangle, Info, X } from 'lucide-react'
import { useNotificationStore, type Notification } from '@/stores/notification-store'
import { cn } from '@/lib/utils'
import { Button } from './button'

const iconMap = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
}

const colorMap = {
  success: 'border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-900 dark:text-green-100',
  error: 'border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-900 dark:text-red-100',
  warning: 'border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-900 dark:text-yellow-100',
  info: 'border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-900 dark:text-blue-100',
}

const iconColorMap = {
  success: 'text-green-600 dark:text-green-400',
  error: 'text-red-600 dark:text-red-400',
  warning: 'text-yellow-600 dark:text-yellow-400',
  info: 'text-blue-600 dark:text-blue-400',
}

interface NotificationItemProps {
  notification: Notification
  onRemove: (id: string) => void
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onRemove }) => {
  const Icon = iconMap[notification.type]
  
  return (
    <div
      className={cn(
        'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border shadow-lg transition-all duration-300 ease-in-out',
        colorMap[notification.type],
        'animate-in slide-in-from-top-full'
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={cn('h-5 w-5', iconColorMap[notification.type])} />
          </div>
          <div className="ml-3 w-0 flex-1">
            {notification.title && (
              <p className="text-sm font-medium">{notification.title}</p>
            )}
            <p className={cn('text-sm', notification.title && 'mt-1')}>
              {notification.message}
            </p>
            {notification.action && (
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={notification.action.onClick}
                  className="text-xs"
                >
                  {notification.action.label}
                </Button>
              </div>
            )}
          </div>
          <div className="ml-4 flex flex-shrink-0">
            <button
              className={cn(
                'inline-flex rounded-md p-1.5 transition-colors hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2',
                iconColorMap[notification.type]
              )}
              onClick={() => onRemove(notification.id)}
              aria-label="Close notification"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Progress bar for timed notifications */}
      {notification.duration && notification.duration > 0 && (
        <div className="h-1 w-full bg-black/10">
          <div
            className="h-full bg-current opacity-50"
            style={{
              animation: `shrink ${notification.duration}ms linear`,
            }}
          />
        </div>
      )}
    </div>
  )
}

export const NotificationSystem: React.FC = () => {
  const { notifications, removeNotification } = useNotificationStore()
  
  if (notifications.length === 0) {
    return null
  }
  
  return (
    <>
      <style jsx>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
      <div
        className="pointer-events-none fixed inset-0 z-50 flex items-end px-4 py-6 sm:items-start sm:p-6"
        aria-live="assertive"
      >
        <div className="flex w-full flex-col items-center space-y-4 sm:items-end">
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
            />
          ))}
        </div>
      </div>
    </>
  )
}

export default NotificationSystem
