/**
 * Health Dashboard Component
 * 
 * Comprehensive health metrics dashboard with supplement correlation analysis.
 */

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchHealthMetrics,
  fetchHealthCorrelations,
  updateHealthFilters,
  selectHealthMetrics,
  selectHealthCorrelations,
  selectHealthLoading,
  selectHealthFilters
} from '@/store/slices/healthSlice';
import { selectUser } from '@/store/slices/authSlice';
import styled from 'styled-components';
import { subDays, format } from 'date-fns';

// Components
import HealthMetricsCard from './HealthMetricsCard';
import WearableDeviceSync from './WearableDeviceSync';
import LineChart from '@/components/analytics/charts/LineChart';
import BarChart from '@/components/analytics/charts/BarChart';
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const HealthIcon = () => <span>🏥</span>;
const CorrelationIcon = () => <span>🔗</span>;
const TrendIcon = () => <span>📈</span>;
const AddIcon = () => <span>+</span>;
const FilterIcon = () => <span>🔽</span>;
const InsightIcon = () => <span>💡</span>;

// Styled components
const DashboardContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`;

const HeaderInfo = styled.div`
  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  
  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
    width: 100%;
  }
`;

const FilterControls = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;
  flex-wrap: wrap;
  
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const FilterSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.9375rem;
  min-width: 150px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  margin-bottom: 2rem;
  overflow-x: auto;
`;

const Tab = styled.button<{ $active: boolean }>`
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: ${props => props.$active ? props.theme.colors.primary : props.theme.colors.textSecondary};
  font-weight: ${props => props.$active ? props.theme.typography.fontWeight.semibold : props.theme.typography.fontWeight.medium};
  border-bottom: 2px solid ${props => props.$active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  
  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const TabContent = styled.div`
  min-height: 400px;
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const ChartsSection = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: ${props => props.theme.breakpoints.desktop}) {
    grid-template-columns: 1fr;
  }
`;

const CorrelationsSection = styled.div`
  margin-bottom: 2rem;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0;
    font-size: 1.25rem;
  }
`;

const CorrelationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const CorrelationCard = styled(Card)`
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const CorrelationInfo = styled.div`
  flex: 1;
`;

const CorrelationTitle = styled.h4`
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
`;

const CorrelationDescription = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
`;

const CorrelationStrength = styled.div<{ $strength: number }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => {
    const abs = Math.abs(props.$strength);
    if (abs >= 0.7) return props.theme.colors.success;
    if (abs >= 0.4) return props.theme.colors.warning;
    return props.theme.colors.textSecondary;
  }};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 1rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

// Tab options
const TABS = [
  { id: 'overview', label: 'Overview', icon: <HealthIcon /> },
  { id: 'metrics', label: 'Health Metrics', icon: <TrendIcon /> },
  { id: 'correlations', label: 'Correlations', icon: <CorrelationIcon /> },
  { id: 'devices', label: 'Connected Devices', icon: <FilterIcon /> },
];

// Filter options
const CATEGORY_OPTIONS = [
  { value: '', label: 'All Categories' },
  { value: 'vitals', label: 'Vital Signs' },
  { value: 'fitness', label: 'Fitness' },
  { value: 'sleep', label: 'Sleep' },
  { value: 'nutrition', label: 'Nutrition' },
  { value: 'mental_health', label: 'Mental Health' },
  { value: 'biomarkers', label: 'Biomarkers' },
];

const TIME_RANGE_OPTIONS = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 3 months' },
  { value: '1y', label: 'Last year' },
];

const HealthDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const healthMetrics = useAppSelector(selectHealthMetrics);
  const correlations = useAppSelector(selectHealthCorrelations);
  const isLoading = useAppSelector(selectHealthLoading);
  const filters = useAppSelector(selectHealthFilters);
  const currentUser = useAppSelector(selectUser);
  
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (currentUser) {
      dispatch(fetchHealthMetrics({
        category: filters.category,
        timeRange: filters.timeRange,
      }));
      
      if (activeTab === 'correlations') {
        dispatch(fetchHealthCorrelations({
          timeRange: filters.timeRange,
        }));
      }
    }
  }, [filters, currentUser, activeTab, dispatch]);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(updateHealthFilters({ category: e.target.value }));
  };

  const handleTimeRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(updateHealthFilters({ timeRange: e.target.value }));
  };

  const handleAddMetric = () => {
    // This would open a modal to add new health metrics
    console.log('Add new health metric');
  };

  if (!currentUser) {
    return (
      <DashboardContainer>
        <EmptyState>
          <h3>Please log in to view health dashboard</h3>
          <p>Sign in to access your personal health metrics and supplement correlations.</p>
        </EmptyState>
      </DashboardContainer>
    );
  }

  // Prepare chart data
  const metricsOverTime = healthMetrics.map(metric => ({
    date: new Date(metric.recorded_at || new Date()),
    value: metric.current_value || 0,
    label: metric.name,
  }));

  const categoryDistribution = CATEGORY_OPTIONS.slice(1).map(category => ({
    label: category.label,
    value: healthMetrics.filter(m => m.category === category.value).length,
  }));

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <TabContent>
            {isLoading ? (
              <LoadingContainer>
                <LoadingSpinner size="large" />
              </LoadingContainer>
            ) : healthMetrics.length === 0 ? (
              <EmptyState>
                <h3>No health metrics yet</h3>
                <p>Start tracking your health metrics to see insights and correlations with your supplements.</p>
                <Button
                  variant="primary"
                  leftIcon={<AddIcon />}
                  onClick={handleAddMetric}
                >
                  Add First Metric
                </Button>
              </EmptyState>
            ) : (
              <>
                <ChartsSection>
                  <div>
                    {metricsOverTime.length > 0 && (
                      <LineChart
                        data={metricsOverTime}
                        title="Health Metrics Trends"
                        xAxisLabel="Date"
                        yAxisLabel="Value"
                        height={300}
                        showDots={true}
                        animate={true}
                      />
                    )}
                  </div>
                  <div>
                    {categoryDistribution.length > 0 && (
                      <BarChart
                        data={categoryDistribution}
                        title="Metrics by Category"
                        orientation="horizontal"
                        height={300}
                        animate={true}
                      />
                    )}
                  </div>
                </ChartsSection>

                <MetricsGrid>
                  {healthMetrics.slice(0, 6).map(metric => (
                    <HealthMetricsCard
                      key={metric.id}
                      metric={metric}
                      showCorrelation={true}
                    />
                  ))}
                </MetricsGrid>
              </>
            )}
          </TabContent>
        );

      case 'metrics':
        return (
          <TabContent>
            {isLoading ? (
              <LoadingContainer>
                <LoadingSpinner size="large" />
              </LoadingContainer>
            ) : (
              <MetricsGrid>
                {healthMetrics.map(metric => (
                  <HealthMetricsCard
                    key={metric.id}
                    metric={metric}
                    showCorrelation={true}
                  />
                ))}
              </MetricsGrid>
            )}
          </TabContent>
        );

      case 'correlations':
        return (
          <TabContent>
            <CorrelationsSection>
              <SectionHeader>
                <CorrelationIcon />
                <h3>Health-Supplement Correlations</h3>
              </SectionHeader>
              
              {isLoading ? (
                <LoadingContainer>
                  <LoadingSpinner size="large" />
                </LoadingContainer>
              ) : correlations.length === 0 ? (
                <EmptyState>
                  <h3>No correlations found</h3>
                  <p>We need more data to identify correlations between your health metrics and supplements.</p>
                </EmptyState>
              ) : (
                <CorrelationsList>
                  {correlations.map((correlation, index) => (
                    <CorrelationCard key={index}>
                      <CorrelationInfo>
                        <CorrelationTitle>
                          {correlation.health_metric} ↔ {correlation.supplement}
                        </CorrelationTitle>
                        <CorrelationDescription>
                          {correlation.description}
                        </CorrelationDescription>
                      </CorrelationInfo>
                      <CorrelationStrength $strength={correlation.strength}>
                        {(correlation.strength * 100).toFixed(1)}%
                      </CorrelationStrength>
                    </CorrelationCard>
                  ))}
                </CorrelationsList>
              )}
            </CorrelationsSection>
          </TabContent>
        );

      case 'devices':
        return (
          <TabContent>
            <WearableDeviceSync />
          </TabContent>
        );

      default:
        return null;
    }
  };

  return (
    <DashboardContainer>
      <DashboardHeader>
        <HeaderInfo>
          <h1>
            <HealthIcon />
            Health Dashboard
          </h1>
          <p>Track your health metrics and discover correlations with your supplements</p>
        </HeaderInfo>
        <HeaderActions>
          <Button
            variant="primary"
            leftIcon={<AddIcon />}
            onClick={handleAddMetric}
          >
            Add Metric
          </Button>
        </HeaderActions>
      </DashboardHeader>

      <FilterControls>
        <FilterSelect
          value={filters.category}
          onChange={handleCategoryChange}
        >
          {CATEGORY_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </FilterSelect>

        <FilterSelect
          value={filters.timeRange}
          onChange={handleTimeRangeChange}
        >
          {TIME_RANGE_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </FilterSelect>
      </FilterControls>

      <TabsContainer>
        {TABS.map(tab => (
          <Tab
            key={tab.id}
            $active={activeTab === tab.id}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon} {tab.label}
          </Tab>
        ))}
      </TabsContainer>

      {renderTabContent()}
    </DashboardContainer>
  );
};

export default HealthDashboard;
