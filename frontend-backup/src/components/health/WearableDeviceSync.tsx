/**
 * Wearable Device Sync Component
 * 
 * Manages connections and data synchronization with wearable devices and health platforms.
 */

import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  fetchConnectedDevices,
  connectDevice,
  disconnectDevice,
  syncDeviceData,
  selectConnectedDevices,
  selectDeviceSyncStatus,
  selectHealthLoading
} from '@/store/slices/healthSlice';
import { showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import LoadingSpinner from '@/components/common/LoadingSpinner';

// Icons
const FitbitIcon = () => <span>⌚</span>;
const AppleHealthIcon = () => <span>🍎</span>;
const GoogleFitIcon = () => <span>🏃</span>;
const GarminIcon = () => <span>🏃‍♂️</span>;
const SamsungHealthIcon = () => <span>📱</span>;
const ConnectedIcon = () => <span style={{ color: '#10b981' }}>✅</span>;
const DisconnectedIcon = () => <span style={{ color: '#ef4444' }}>❌</span>;
const SyncIcon = () => <span>🔄</span>;
const SettingsIcon = () => <span>⚙️</span>;

// Styled components
const SyncContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const SectionHeader = styled.div`
  margin-bottom: 1rem;
  
  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
  }
  
  p {
    color: ${props => props.theme.colors.textSecondary};
    margin: 0;
    line-height: 1.5;
  }
`;

const DevicesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const DeviceCard = styled(Card)<{ $connected: boolean }>`
  padding: 1.5rem;
  border: 2px solid ${props => props.$connected ? props.theme.colors.success : props.theme.colors.border};
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px ${props => props.theme.colors.shadow}15;
  }
`;

const DeviceHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const DeviceIcon = styled.div`
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  background: ${props => props.theme.colors.backgroundSecondary};
`;

const DeviceInfo = styled.div`
  flex: 1;
`;

const DeviceName = styled.h4`
  color: ${props => props.theme.colors.text};
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
`;

const DeviceDescription = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.4;
`;

const DeviceStatus = styled.div<{ $connected: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.$connected ? props.theme.colors.success : props.theme.colors.textSecondary};
`;

const DeviceMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.medium};
`;

const MetricItem = styled.div`
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 1.25rem;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  color: ${props => props.theme.colors.primary};
  margin-bottom: 0.25rem;
`;

const MetricLabel = styled.div`
  font-size: 0.75rem;
  color: ${props => props.theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const DeviceActions = styled.div`
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
`;

const SyncStatus = styled.div<{ $syncing: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: ${props => props.$syncing ? props.theme.colors.primary : props.theme.colors.textSecondary};
  margin-top: 0.5rem;
`;

const LastSyncTime = styled.div`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
  margin-top: 0.25rem;
`;

// Device configurations
const SUPPORTED_DEVICES = [
  {
    id: 'fitbit',
    name: 'Fitbit',
    description: 'Sync steps, heart rate, sleep, and activity data',
    icon: <FitbitIcon />,
    metrics: ['steps', 'heart_rate', 'sleep_hours', 'calories_burned'],
    authUrl: '/auth/fitbit',
  },
  {
    id: 'apple_health',
    name: 'Apple Health',
    description: 'Comprehensive health data from iPhone and Apple Watch',
    icon: <AppleHealthIcon />,
    metrics: ['steps', 'heart_rate', 'sleep_hours', 'blood_pressure', 'weight'],
    authUrl: '/auth/apple-health',
  },
  {
    id: 'google_fit',
    name: 'Google Fit',
    description: 'Activity tracking and health metrics from Android devices',
    icon: <GoogleFitIcon />,
    metrics: ['steps', 'heart_rate', 'weight', 'calories_burned'],
    authUrl: '/auth/google-fit',
  },
  {
    id: 'garmin',
    name: 'Garmin Connect',
    description: 'Advanced fitness and health metrics from Garmin devices',
    icon: <GarminIcon />,
    metrics: ['steps', 'heart_rate', 'sleep_hours', 'stress_level', 'vo2_max'],
    authUrl: '/auth/garmin',
  },
  {
    id: 'samsung_health',
    name: 'Samsung Health',
    description: 'Health and fitness data from Samsung devices',
    icon: <SamsungHealthIcon />,
    metrics: ['steps', 'heart_rate', 'sleep_hours', 'blood_pressure'],
    authUrl: '/auth/samsung-health',
  },
];

const WearableDeviceSync: React.FC = () => {
  const dispatch = useAppDispatch();
  const connectedDevices = useAppSelector(selectConnectedDevices);
  const syncStatus = useAppSelector(selectDeviceSyncStatus);
  const isLoading = useAppSelector(selectHealthLoading);
  
  const [syncingDevices, setSyncingDevices] = useState<Set<string>>(new Set());

  useEffect(() => {
    dispatch(fetchConnectedDevices());
  }, [dispatch]);

  const handleConnectDevice = async (deviceId: string) => {
    try {
      const device = SUPPORTED_DEVICES.find(d => d.id === deviceId);
      if (!device) return;

      // In a real implementation, this would open OAuth flow
      window.open(device.authUrl, '_blank', 'width=600,height=600');
      
      // For demo purposes, simulate connection
      await dispatch(connectDevice({
        device_id: deviceId,
        device_name: device.name,
        auth_token: 'demo-token',
      })).unwrap();

      dispatch(showSuccessNotification(
        `Successfully connected to ${device.name}`,
        'Device Connection'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to connect device',
        'Connection Error'
      ));
    }
  };

  const handleDisconnectDevice = async (deviceId: string) => {
    if (!window.confirm('Are you sure you want to disconnect this device?')) {
      return;
    }

    try {
      await dispatch(disconnectDevice(deviceId)).unwrap();
      dispatch(showSuccessNotification(
        'Device disconnected successfully',
        'Device Connection'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to disconnect device',
        'Disconnection Error'
      ));
    }
  };

  const handleSyncDevice = async (deviceId: string) => {
    setSyncingDevices(prev => new Set(prev).add(deviceId));
    
    try {
      await dispatch(syncDeviceData(deviceId)).unwrap();
      dispatch(showSuccessNotification(
        'Device data synced successfully',
        'Data Sync'
      ));
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to sync device data',
        'Sync Error'
      ));
    } finally {
      setSyncingDevices(prev => {
        const newSet = new Set(prev);
        newSet.delete(deviceId);
        return newSet;
      });
    }
  };

  const isDeviceConnected = (deviceId: string) => {
    return connectedDevices.some(device => device.device_id === deviceId);
  };

  const getConnectedDevice = (deviceId: string) => {
    return connectedDevices.find(device => device.device_id === deviceId);
  };

  const formatLastSync = (lastSync: string | undefined) => {
    if (!lastSync) return 'Never';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`;
    return `${Math.floor(diffMins / 1440)} days ago`;
  };

  if (isLoading) {
    return (
      <SyncContainer>
        <div style={{ display: 'flex', justifyContent: 'center', padding: '2rem' }}>
          <LoadingSpinner size="large" />
        </div>
      </SyncContainer>
    );
  }

  return (
    <SyncContainer>
      <SectionHeader>
        <h3>Wearable Device Integration</h3>
        <p>
          Connect your wearable devices and health platforms to automatically sync health metrics 
          and correlate them with your supplement intake for personalized insights.
        </p>
      </SectionHeader>

      <DevicesGrid>
        {SUPPORTED_DEVICES.map(device => {
          const connected = isDeviceConnected(device.id);
          const connectedDevice = getConnectedDevice(device.id);
          const isSyncing = syncingDevices.has(device.id);

          return (
            <DeviceCard key={device.id} $connected={connected}>
              <DeviceHeader>
                <DeviceIcon>{device.icon}</DeviceIcon>
                <DeviceInfo>
                  <DeviceName>{device.name}</DeviceName>
                  <DeviceDescription>{device.description}</DeviceDescription>
                </DeviceInfo>
                <DeviceStatus $connected={connected}>
                  {connected ? <ConnectedIcon /> : <DisconnectedIcon />}
                  {connected ? 'Connected' : 'Not Connected'}
                </DeviceStatus>
              </DeviceHeader>

              {connected && connectedDevice && (
                <>
                  <DeviceMetrics>
                    <MetricItem>
                      <MetricValue>{connectedDevice.metrics_synced || 0}</MetricValue>
                      <MetricLabel>Metrics Synced</MetricLabel>
                    </MetricItem>
                    <MetricItem>
                      <MetricValue>{connectedDevice.data_points || 0}</MetricValue>
                      <MetricLabel>Data Points</MetricLabel>
                    </MetricItem>
                    <MetricItem>
                      <MetricValue>
                        {connectedDevice.sync_frequency || 'Daily'}
                      </MetricValue>
                      <MetricLabel>Sync Frequency</MetricLabel>
                    </MetricItem>
                  </DeviceMetrics>

                  <SyncStatus $syncing={isSyncing}>
                    {isSyncing ? <LoadingSpinner size="small" /> : <SyncIcon />}
                    {isSyncing ? 'Syncing...' : 'Ready to sync'}
                  </SyncStatus>

                  <LastSyncTime>
                    Last sync: {formatLastSync(connectedDevice.last_sync)}
                  </LastSyncTime>
                </>
              )}

              <DeviceActions>
                {connected ? (
                  <>
                    <Button
                      variant="primary"
                      size="small"
                      leftIcon={isSyncing ? <LoadingSpinner size="small" /> : <SyncIcon />}
                      onClick={() => handleSyncDevice(device.id)}
                      disabled={isSyncing}
                    >
                      {isSyncing ? 'Syncing...' : 'Sync Now'}
                    </Button>
                    <Button
                      variant="outline"
                      size="small"
                      leftIcon={<SettingsIcon />}
                      onClick={() => {/* Open device settings */}}
                    >
                      Settings
                    </Button>
                    <Button
                      variant="ghost"
                      size="small"
                      onClick={() => handleDisconnectDevice(device.id)}
                    >
                      Disconnect
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="primary"
                    size="small"
                    onClick={() => handleConnectDevice(device.id)}
                    style={{ width: '100%' }}
                  >
                    Connect {device.name}
                  </Button>
                )}
              </DeviceActions>
            </DeviceCard>
          );
        })}
      </DevicesGrid>
    </SyncContainer>
  );
};

export default WearableDeviceSync;
