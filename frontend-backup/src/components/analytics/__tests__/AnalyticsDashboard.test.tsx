/**
 * AnalyticsDashboard Component Tests
 * 
 * Test-driven development tests for the AnalyticsDashboard component.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { configureStore } from '@reduxjs/toolkit';

// Components and store
import AnalyticsDashboard from '../AnalyticsDashboard';
import authReducer from '@/store/slices/authSlice';
import analyticsReducer from '@/store/slices/analyticsSlice';
import uiReducer from '@/store/slices/uiSlice';
import { lightTheme } from '@/styles/theme';

// Test data
const mockAnalyticsData = {
  metrics: {
    totalSupplements: 12,
    adherenceRate: 85,
    totalIntakes: 342,
    streakDays: 15,
    supplementsChange: 2,
    adherenceChange: 5,
    intakesChange: 12,
    streakChange: 3,
  },
  trends: {
    adherence: [
      { date: '2024-01-01T00:00:00Z', value: 80 },
      { date: '2024-01-02T00:00:00Z', value: 85 },
      { date: '2024-01-03T00:00:00Z', value: 90 },
      { date: '2024-01-04T00:00:00Z', value: 85 },
      { date: '2024-01-05T00:00:00Z', value: 88 },
    ],
    intakes: [
      { date: '2024-01-01T00:00:00Z', value: 8 },
      { date: '2024-01-02T00:00:00Z', value: 10 },
      { date: '2024-01-03T00:00:00Z', value: 12 },
      { date: '2024-01-04T00:00:00Z', value: 9 },
      { date: '2024-01-05T00:00:00Z', value: 11 },
    ],
    supplements: [],
  },
  distribution: {
    supplements: [
      { name: 'Vitamin D3', count: 30, color: '#3b82f6' },
      { name: 'Magnesium', count: 25, color: '#10b981' },
      { name: 'Omega-3', count: 20, color: '#f59e0b' },
      { name: 'Vitamin B12', count: 15, color: '#ef4444' },
      { name: 'Zinc', count: 10, color: '#8b5cf6' },
    ],
    categories: [
      { name: 'Vitamins', count: 45 },
      { name: 'Minerals', count: 35 },
      { name: 'Fatty Acids', count: 20 },
    ],
    brands: [
      { name: 'Brand A', count: 40 },
      { name: 'Brand B', count: 35 },
      { name: 'Brand C', count: 25 },
    ],
  },
  insights: [
    {
      type: 'positive' as const,
      text: 'Your adherence rate has improved by 5% this month. Keep up the great work!',
      confidence: 0.9,
    },
    {
      type: 'recommendation' as const,
      text: 'Consider taking your magnesium supplement in the evening for better sleep quality.',
      confidence: 0.8,
    },
    {
      type: 'neutral' as const,
      text: 'Your supplement intake is consistent across weekdays and weekends.',
      confidence: 0.7,
    },
  ],
};

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
      analytics: analyticsReducer,
      ui: uiReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Test User',
          is_active: true,
          is_superuser: false,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
      analytics: {
        data: mockAnalyticsData,
        isLoading: false,
        isExporting: false,
        error: null,
        filters: {
          timeRange: '30d',
          supplementIds: [],
          groupBy: 'day',
        },
        lastUpdated: '2024-01-05T12:00:00Z',
      },
      ui: {
        theme: 'light',
        sidebarOpen: true,
        notifications: [],
        modals: [],
        globalLoading: false,
        pageTitle: 'Analytics',
        breadcrumbs: [],
        searchQuery: '',
        mobileMenuOpen: false,
      },
      ...initialState,
    },
  });
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      <ThemeProvider theme={lightTheme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  </Provider>
);

describe('AnalyticsDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render dashboard header correctly', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Track your supplement journey with detailed insights and trends')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /export data/i })).toBeInTheDocument();
    });

    it('should render metrics cards with correct values', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('12')).toBeInTheDocument(); // Total supplements
      expect(screen.getByText('85%')).toBeInTheDocument(); // Adherence rate
      expect(screen.getByText('342')).toBeInTheDocument(); // Total intakes
      expect(screen.getByText('15')).toBeInTheDocument(); // Streak days

      expect(screen.getByText('Total Supplements')).toBeInTheDocument();
      expect(screen.getByText('Adherence Rate')).toBeInTheDocument();
      expect(screen.getByText('Total Intakes')).toBeInTheDocument();
      expect(screen.getByText('Current Streak')).toBeInTheDocument();
    });

    it('should render metric changes with correct trends', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('2% vs last period')).toBeInTheDocument();
      expect(screen.getByText('5% vs last period')).toBeInTheDocument();
      expect(screen.getByText('12% vs last period')).toBeInTheDocument();
      expect(screen.getByText('3 days vs last period')).toBeInTheDocument();
    });

    it('should render filter controls', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue('Last 30 days')).toBeInTheDocument();
    });

    it('should render charts when data is available', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Adherence Trend')).toBeInTheDocument();
      expect(screen.getByText('Supplements by Category')).toBeInTheDocument();
      expect(screen.getByText('Supplement Distribution')).toBeInTheDocument();
    });

    it('should render insights section', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('AI-Powered Insights')).toBeInTheDocument();
      expect(screen.getByText(/Your adherence rate has improved/)).toBeInTheDocument();
      expect(screen.getByText(/Consider taking your magnesium/)).toBeInTheDocument();
      expect(screen.getByText(/Your supplement intake is consistent/)).toBeInTheDocument();
    });
  });

  describe('Filter Interactions', () => {
    it('should handle time range filter changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      await user.selectOptions(timeRangeSelect, 'Last 7 days');

      expect(timeRangeSelect).toHaveValue('7d');
    });

    it('should show custom date inputs when custom range is selected', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      await user.selectOptions(timeRangeSelect, 'Custom range');

      expect(screen.getAllByDisplayValue(/2024-/)).toHaveLength(2); // Start and end date inputs
    });

    it('should handle custom date range changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      await user.selectOptions(timeRangeSelect, 'Custom range');

      const dateInputs = screen.getAllByDisplayValue(/2024-/);
      await user.clear(dateInputs[0]);
      await user.type(dateInputs[0], '2024-01-01');

      expect(dateInputs[0]).toHaveValue('2024-01-01');
    });
  });

  describe('Export Functionality', () => {
    it('should handle export button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      const exportButton = screen.getByRole('button', { name: /export data/i });
      await user.click(exportButton);

      // Would verify export action dispatch in real implementation
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner when data is loading', () => {
      const storeWithLoading = createTestStore({
        analytics: {
          data: null,
          isLoading: true,
          isExporting: false,
          error: null,
          filters: {
            timeRange: '30d',
            supplementIds: [],
            groupBy: 'day',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithLoading}>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('should show empty state when no data is available', () => {
      const storeWithoutData = createTestStore({
        analytics: {
          data: null,
          isLoading: false,
          isExporting: false,
          error: null,
          filters: {
            timeRange: '30d',
            supplementIds: [],
            groupBy: 'day',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutData}>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('No data available')).toBeInTheDocument();
      expect(screen.getByText('Start tracking your supplements to see analytics and insights here.')).toBeInTheDocument();
    });

    it('should show login prompt when user is not authenticated', () => {
      const storeWithoutUser = createTestStore({
        auth: {
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        },
      });

      render(
        <TestWrapper store={storeWithoutUser}>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Please log in to view analytics')).toBeInTheDocument();
      expect(screen.getByText('Sign in to access your personal supplement tracking analytics and insights.')).toBeInTheDocument();
    });
  });

  describe('Chart Interactions', () => {
    it('should render charts with proper data', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      // Check for chart containers
      expect(screen.getByText('Adherence Trend')).toBeInTheDocument();
      expect(screen.getByText('Supplements by Category')).toBeInTheDocument();
      expect(screen.getByText('Supplement Distribution')).toBeInTheDocument();
    });

    it('should handle chart interactions', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      // Charts should be interactive (would test specific chart interactions)
      const chartContainers = screen.getAllByRole('img', { hidden: true }); // SVG charts
      expect(chartContainers.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Design', () => {
    it('should handle mobile layout', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      // Should still render main content
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Total Supplements')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle analytics errors gracefully', () => {
      const storeWithError = createTestStore({
        analytics: {
          data: null,
          isLoading: false,
          isExporting: false,
          error: 'Failed to load analytics data',
          filters: {
            timeRange: '30d',
            supplementIds: [],
            groupBy: 'day',
          },
          lastUpdated: null,
        },
      });

      render(
        <TestWrapper store={storeWithError}>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      // Should show empty state when there's an error
      expect(screen.getByText('No data available')).toBeInTheDocument();
    });
  });

  describe('Data Formatting', () => {
    it('should format metric values correctly', () => {
      const storeWithLargeNumbers = createTestStore({
        analytics: {
          data: {
            ...mockAnalyticsData,
            metrics: {
              ...mockAnalyticsData.metrics,
              totalIntakes: 1234,
              totalSupplements: 25,
            },
          },
          isLoading: false,
          isExporting: false,
          error: null,
          filters: {
            timeRange: '30d',
            supplementIds: [],
            groupBy: 'day',
          },
          lastUpdated: '2024-01-05T12:00:00Z',
        },
      });

      render(
        <TestWrapper store={storeWithLargeNumbers}>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('1234')).toBeInTheDocument(); // Should format large numbers
      expect(screen.getByText('25')).toBeInTheDocument();
    });

    it('should handle percentage formatting', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('85%')).toBeInTheDocument(); // Adherence rate
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Analytics Dashboard');
      expect(screen.getByRole('heading', { level: 3 })).toHaveTextContent('AI-Powered Insights');
    });

    it('should have accessible form controls', () => {
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      const timeRangeSelect = screen.getByDisplayValue('Last 30 days');
      expect(timeRangeSelect).toBeInTheDocument();
      expect(timeRangeSelect).toHaveAttribute('aria-label');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AnalyticsDashboard />
        </TestWrapper>
      );

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByDisplayValue('Last 30 days')).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /export data/i })).toHaveFocus();
    });
  });
});
