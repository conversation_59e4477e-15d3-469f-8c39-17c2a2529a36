/**
 * Tracking Page Component
 *
 * Dedicated page for supplement intake tracking and quick logging.
 */

import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { setPageTitle, setBreadcrumbs } from '@/store/slices/uiSlice';
import { selectRecentIntakes } from '@/store/slices/supplementSlice';
import styled from 'styled-components';

// Components
import Card from '@/components/common/Card';
import SupplementSearch from '@/components/supplements/SupplementSearch';
import IntakeHistory from '@/components/supplements/IntakeHistory';
import QuickAddWidget from '@/components/supplements/QuickAddWidget';

// Styled components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const PageHeader = styled.div`
  margin-bottom: 2rem;

  h1 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    font-size: 1.1rem;
    margin: 0;
  }
`;

const TrackingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const QuickActionsCard = styled(Card)`
  padding: 1.5rem;

  h3 {
    color: ${props => props.theme.colors.text};
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
  }
`;

const TrackingPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const recentIntakes = useAppSelector(selectRecentIntakes);

  useEffect(() => {
    dispatch(setPageTitle('Supplement Tracking'));
    dispatch(setBreadcrumbs([
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Tracking' },
    ]));
  }, [dispatch]);

  return (
    <PageContainer>
      <PageHeader>
        <h1>Supplement Tracking</h1>
        <p>
          Log your daily supplement intake and track your progress
        </p>
      </PageHeader>

      <TrackingGrid>
        <MainContent>
          <SupplementSearch
            showQuickAdd
            compact
          />

          <IntakeHistory
            showStats
          />
        </MainContent>

        <Sidebar>
          <QuickActionsCard>
            <h3>Quick Actions</h3>
            <QuickAddWidget />
          </QuickActionsCard>
        </Sidebar>
      </TrackingGrid>
    </PageContainer>
  );
};

export default TrackingPage;
