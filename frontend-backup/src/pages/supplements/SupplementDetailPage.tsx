/**
 * Supplement Detail Page Component
 *
 * Detailed view of a specific supplement with tracking capabilities.
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchSupplement,
  clearCurrentSupplement,
  selectCurrentSupplement,
  selectSupplementsLoading,
  trackSupplementIntake,
  selectTrackingIntake
} from '@/store/slices/supplementSlice';
import { setPageTitle, setBreadcrumbs, showSuccessNotification, showErrorNotification } from '@/store/slices/uiSlice';
import styled from 'styled-components';

// Components
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import QuickTrackModal from '@/components/supplements/QuickTrackModal';
import IntakeHistory from '@/components/supplements/IntakeHistory';

// Icons
const BackIcon = () => <span>←</span>;
const TrackIcon = () => <span>📝</span>;
const ShareIcon = () => <span>🔗</span>;

// Styled components
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const BackButton = styled(Button)`
  margin-bottom: 1rem;
`;

const SupplementHeader = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const SupplementImage = styled.div<{ $imageUrl?: string }>`
  width: 100%;
  height: 300px;
  background: ${props =>
    props.$imageUrl
      ? `url(${props.$imageUrl}) center/cover`
      : `linear-gradient(135deg, ${props.theme.colors.primary}20, ${props.theme.colors.secondary}20)`
  };
  border-radius: ${props => props.theme.borderRadius.large};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.textSecondary};
  font-size: 4rem;
  border: 1px solid ${props => props.theme.colors.border};
`;

const SupplementInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SupplementTitle = styled.h1`
  color: ${props => props.theme.colors.text};
  margin: 0;
  font-size: 2.5rem;
  line-height: 1.2;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    font-size: 2rem;
  }
`;

const SupplementBrand = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 1.2rem;
  margin: 0;
`;

const SupplementCategory = styled.span`
  display: inline-block;
  background: ${props => props.theme.colors.primary}15;
  color: ${props => props.theme.colors.primary};
  padding: 0.5rem 1rem;
  border-radius: ${props => props.theme.borderRadius.medium};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  width: fit-content;
`;

const SupplementDescription = styled.p`
  color: ${props => props.theme.colors.text};
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 1rem 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: auto;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`;

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
`;

const ErrorContainer = styled.div`
  text-align: center;
  padding: 3rem 1rem;

  h2 {
    color: ${props => props.theme.colors.text};
    margin-bottom: 1rem;
  }

  p {
    color: ${props => props.theme.colors.textSecondary};
    margin-bottom: 2rem;
  }
`;

const SupplementDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const supplement = useAppSelector(selectCurrentSupplement);
  const isLoading = useAppSelector(selectSupplementsLoading);
  const isTracking = useAppSelector(selectTrackingIntake);

  const [showQuickTrack, setShowQuickTrack] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(fetchSupplement(id));
    }

    return () => {
      dispatch(clearCurrentSupplement());
    };
  }, [id, dispatch]);

  useEffect(() => {
    if (supplement) {
      dispatch(setPageTitle(supplement.name));
      dispatch(setBreadcrumbs([
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Supplements', href: '/supplements' },
        { label: supplement.name },
      ]));
    }
  }, [supplement, dispatch]);

  const handleBack = () => {
    navigate('/supplements');
  };

  const handleQuickTrack = async (dosage: number, dosageUnit: string, notes?: string) => {
    if (!supplement) return;

    try {
      await dispatch(trackSupplementIntake({
        supplementId: supplement.id,
        intakeData: {
          dosage,
          dosage_unit: dosageUnit,
          taken_at: new Date().toISOString(),
          notes,
        },
      })).unwrap();

      dispatch(showSuccessNotification(
        `Successfully logged ${dosage}${dosageUnit} of ${supplement.name}`,
        'Intake Recorded'
      ));
      setShowQuickTrack(false);
    } catch (error: any) {
      dispatch(showErrorNotification(
        error.message || 'Failed to log supplement intake',
        'Tracking Error'
      ));
    }
  };

  const formatCategory = (category: string) => {
    return category
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (isLoading && !supplement) {
    return (
      <PageContainer>
        <LoadingContainer>
          <LoadingSpinner size="large" />
        </LoadingContainer>
      </PageContainer>
    );
  }

  if (!supplement && !isLoading) {
    return (
      <PageContainer>
        <ErrorContainer>
          <h2>Supplement Not Found</h2>
          <p>The supplement you're looking for doesn't exist or has been removed.</p>
          <Button variant="primary" onClick={handleBack}>
            Back to Supplements
          </Button>
        </ErrorContainer>
      </PageContainer>
    );
  }

  if (!supplement) return null;

  return (
    <PageContainer>
      <BackButton
        variant="ghost"
        onClick={handleBack}
        leftIcon={<BackIcon />}
      >
        Back to Supplements
      </BackButton>

      <SupplementHeader>
        <SupplementImage $imageUrl={supplement.image_url}>
          {!supplement.image_url && '💊'}
        </SupplementImage>

        <SupplementInfo>
          <div>
            <SupplementTitle>{supplement.name}</SupplementTitle>
            {supplement.brand && (
              <SupplementBrand>by {supplement.brand}</SupplementBrand>
            )}
            <SupplementCategory>
              {formatCategory(supplement.category)}
            </SupplementCategory>
          </div>

          {supplement.description && (
            <SupplementDescription>
              {supplement.description}
            </SupplementDescription>
          )}

          <ActionButtons>
            <Button
              variant="primary"
              onClick={() => setShowQuickTrack(true)}
              disabled={isTracking}
              leftIcon={isTracking ? <LoadingSpinner size="small" /> : <TrackIcon />}
            >
              {isTracking ? 'Logging...' : 'Log Intake'}
            </Button>
            <Button
              variant="outline"
              leftIcon={<ShareIcon />}
            >
              Share
            </Button>
          </ActionButtons>
        </SupplementInfo>
      </SupplementHeader>

      <ContentGrid>
        <MainContent>
          <IntakeHistory supplementId={supplement.id} />
        </MainContent>

        <Sidebar>
          {/* Supplement details card will be added here */}
        </Sidebar>
      </ContentGrid>

      {showQuickTrack && (
        <QuickTrackModal
          supplement={supplement}
          onTrack={handleQuickTrack}
          onClose={() => setShowQuickTrack(false)}
          isLoading={isTracking}
        />
      )}
    </PageContainer>
  );
};

export default SupplementDetailPage;
