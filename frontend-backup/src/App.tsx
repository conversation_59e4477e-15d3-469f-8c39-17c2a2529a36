/**
 * Main App Component
 * 
 * Root component that sets up routing, authentication, and global providers.
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'styled-components';
import { ThemeProvider as ModernThemeProvider } from '@/providers/theme-provider';

// i18n
import './i18n';

// Store and theme
import { store } from '@/store';
import { useAppDispatch, useAppSelector } from '@/store';
import { initializeAuth, selectIsAuthenticated } from '@/store/slices/authSlice';
import { selectTheme } from '@/store/slices/uiSlice';

// Components
import Layout from '@/components/layout/Layout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import NotificationContainer from '@/components/common/NotificationContainer';
import ModalContainer from '@/components/common/ModalContainer';
import NotificationSystem from '@/components/ui/notification-system';

// Pages
import LoginPage from '@/pages/auth/LoginPage';
import RegisterPage from '@/pages/auth/RegisterPage';
import DashboardPage from '@/pages/dashboard/DashboardPage';
import SupplementsPage from '@/pages/supplements/SupplementsPage';
import SupplementDetailPage from '@/pages/supplements/SupplementDetailPage';
import TrackingPage from '@/pages/tracking/TrackingPage';
import ResearchPage from '@/pages/research/ResearchPage';
import ResearchProtocolPage from '@/pages/research/ResearchProtocolPage';
import CommunityPage from '@/pages/community/CommunityPage';
import CommunityPostPage from '@/pages/community/CommunityPostPage';
import AnalyticsPage from '@/pages/analytics/AnalyticsPage';
import HealthPage from '@/pages/health/HealthPage';
import ProfilePage from '@/pages/profile/ProfilePage';
import SettingsPage from '@/pages/settings/SettingsPage';

// Styles
import './index.css';
import NotFoundPage from '@/pages/NotFoundPage';

// Themes
import { lightTheme, darkTheme } from '@/styles/theme';
import GlobalStyles from '@/styles/GlobalStyles';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// App content component (needs to be inside providers to access hooks)
const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const theme = useAppSelector(selectTheme);
  const [isInitialized, setIsInitialized] = React.useState(false);

  // Initialize authentication on app start
  useEffect(() => {
    dispatch(initializeAuth());
    setIsInitialized(true);
  }, [dispatch]);

  // Determine current theme
  const currentTheme = React.useMemo(() => {
    if (theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? darkTheme : lightTheme;
    }
    return theme === 'dark' ? darkTheme : lightTheme;
  }, [theme]);

  // Show loading spinner while initializing
  if (!isInitialized) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <ModernThemeProvider>
      <ThemeProvider theme={currentTheme}>
        <GlobalStyles />
        <Router>
        <Routes>
          {/* Public routes */}
          <Route 
            path="/login" 
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
            } 
          />
          <Route 
            path="/register" 
            element={
              isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
            } 
          />

          {/* Protected routes */}
          <Route path="/" element={<ProtectedRoute />}>
            <Route element={<Layout />}>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<DashboardPage />} />
              
              {/* Supplement routes */}
              <Route path="supplements" element={<SupplementsPage />} />
              <Route path="supplements/:id" element={<SupplementDetailPage />} />
              <Route path="tracking" element={<TrackingPage />} />
              
              {/* Research routes */}
              <Route path="research" element={<ResearchPage />} />
              <Route path="research/protocols/:id" element={<ResearchProtocolPage />} />
              
              {/* Community routes */}
              <Route path="community" element={<CommunityPage />} />
              <Route path="community/posts/:id" element={<CommunityPostPage />} />
              
              {/* Analytics and health routes */}
              <Route path="analytics" element={<AnalyticsPage />} />
              <Route path="health" element={<HealthPage />} />
              <Route path="profile" element={<ProfilePage />} />
              <Route path="settings" element={<SettingsPage />} />
            </Route>
          </Route>

          {/* 404 route */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* Global components */}
        <NotificationContainer />
        <NotificationSystem />
        <ModalContainer />
        </Router>
      </ThemeProvider>
    </ModernThemeProvider>
  );
};

// Main App component
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <AppContent />
      </QueryClientProvider>
    </Provider>
  );
};

export default App;
