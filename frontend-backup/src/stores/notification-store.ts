import { create } from 'zustand'

export interface Notification {
  id: string
  title?: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  persistent?: boolean
  timestamp: number
}

interface NotificationStore {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearAllNotifications: () => void
  updateNotification: (id: string, updates: Partial<Notification>) => void
}

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],
  
  addNotification: (notification) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
      duration: notification.duration ?? (notification.type === 'error' ? 0 : 5000),
    }
    
    set((state) => ({
      notifications: [...state.notifications, newNotification],
    }))
    
    // Auto-remove notification after duration (if not persistent)
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        get().removeNotification(id)
      }, newNotification.duration)
    }
  },
  
  removeNotification: (id) => {
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    }))
  },
  
  clearAllNotifications: () => {
    set({ notifications: [] })
  },
  
  updateNotification: (id, updates) => {
    set((state) => ({
      notifications: state.notifications.map((n) =>
        n.id === id ? { ...n, ...updates } : n
      ),
    }))
  },
}))

// Helper functions for common notification types
export const notify = {
  success: (message: string, title?: string, options?: Partial<Notification>) => {
    useNotificationStore.getState().addNotification({
      type: 'success',
      message,
      title,
      ...options,
    })
  },
  
  error: (message: string, title?: string, options?: Partial<Notification>) => {
    useNotificationStore.getState().addNotification({
      type: 'error',
      message,
      title,
      duration: 0, // Error notifications persist by default
      ...options,
    })
  },
  
  warning: (message: string, title?: string, options?: Partial<Notification>) => {
    useNotificationStore.getState().addNotification({
      type: 'warning',
      message,
      title,
      ...options,
    })
  },
  
  info: (message: string, title?: string, options?: Partial<Notification>) => {
    useNotificationStore.getState().addNotification({
      type: 'info',
      message,
      title,
      ...options,
    })
  },
}
