<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="description" content="Evidence-based supplement research and tracking platform" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <title>Supplement Tracker - Evidence-Based Research Platform</title>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.6;
        color: #1f2937;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
      }

      .header h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }

      .header p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
      }

      .cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
      }

      .card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
      }

      .card h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #2563eb;
      }

      .card p {
        color: #6b7280;
        margin-bottom: 1.5rem;
      }

      .btn {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background: #2563eb;
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        transition: background 0.3s ease;
      }

      .btn:hover {
        background: #1d4ed8;
      }

      .btn-secondary {
        background: #10b981;
      }

      .btn-secondary:hover {
        background: #059669;
      }

      .status {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        text-align: center;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: #10b981;
        border-radius: 50%;
        margin-right: 0.5rem;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .footer {
        text-align: center;
        color: white;
        opacity: 0.8;
        margin-top: 3rem;
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2rem;
        }

        .container {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🏥 Supplement Tracker</h1>
        <p>Evidence-based supplement research and tracking platform with port-free access</p>
      </div>

      <div class="cards">
        <div class="card">
          <h3>🧪 Interactive Demo</h3>
          <p>Explore the full-featured demo with API testing, supplement tracking, and interactive features.</p>
          <a href="/demo.html" class="btn">Launch Demo</a>
        </div>

        <div class="card">
          <h3>📚 API Documentation</h3>
          <p>Comprehensive API documentation with interactive testing capabilities powered by FastAPI.</p>
          <a href="https://api.pills.localhost/docs" class="btn btn-secondary">View API Docs</a>
        </div>

        <div class="card">
          <h3>🔧 Service Dashboard</h3>
          <p>Monitor service health, routing, and performance through the Traefik management dashboard.</p>
          <a href="http://traefik.pills.localhost:9081/" class="btn">Open Dashboard</a>
        </div>
      </div>

      <div class="status">
        <h3>
          <span class="status-indicator"></span>
          System Status: All Services Online
        </h3>
        <p>
          ✅ Frontend: <strong>https://app.pills.localhost/</strong><br>
          ✅ API: <strong>https://api.pills.localhost/</strong><br>
          ✅ Dashboard: <strong>http://traefik.pills.localhost:9081/</strong>
        </p>
      </div>

      <div class="footer">
        <p>Powered by FastAPI, Traefik, and Docker • Port-free architecture with HTTPS/HTTP support</p>
      </div>
    </div>
  </body>
</html>
