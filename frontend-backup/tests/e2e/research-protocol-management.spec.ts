/**
 * Research Protocol Management E2E Tests
 * 
 * End-to-end tests for research protocol creation, discovery, and participation workflows.
 */

import { test, expect, Page } from '@playwright/test';

// Test data
const testProtocol = {
  title: 'Omega-3 Cognitive Enhancement Study',
  description: 'A randomized controlled trial examining the effects of omega-3 fatty acids on cognitive function in healthy adults.',
  duration: '16',
  maxParticipants: '50',
  objectives: [
    'Measure cognitive performance improvements',
    'Assess memory function changes',
    'Monitor omega-3 blood levels'
  ],
  primaryEndpoints: [
    'Cognitive assessment scores',
    'Memory test results'
  ],
  secondaryEndpoints: [
    'Blood omega-3 levels',
    'Quality of life measures'
  ],
  inclusionCriteria: [
    'Age 25-55 years',
    'Healthy adults',
    'No current omega-3 supplementation'
  ],
  exclusionCriteria: [
    'Fish allergy',
    'Pregnancy or nursing',
    'Current psychiatric medication'
  ],
  supplements: ['Omega-3 EPA/DHA'],
  dataCollection: 'Weekly cognitive assessments and monthly blood tests for omega-3 levels',
  ethicalConsiderations: 'IRB approved study with full informed consent. Minimal risk intervention with established safety profile.',
  consentUrl: 'https://example.com/omega3-consent.pdf'
};

// Helper functions
async function loginAsResearcher(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('/dashboard');
}

async function loginAsParticipant(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'password123');
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('/dashboard');
}

async function navigateToResearch(page: Page) {
  await page.click('[data-testid="nav-research"]');
  await expect(page).toHaveURL('/research');
  await expect(page.locator('h1')).toContainText('Research Hub');
}

async function fillProtocolBasicInfo(page: Page) {
  await page.fill('#title', testProtocol.title);
  await page.fill('#description', testProtocol.description);
  await page.fill('#duration_weeks', testProtocol.duration);
  await page.fill('#max_participants', testProtocol.maxParticipants);
}

async function fillProtocolObjectives(page: Page) {
  // Fill objectives
  for (let i = 0; i < testProtocol.objectives.length; i++) {
    if (i > 0) {
      await page.click('button:has-text("Add Objective")');
    }
    await page.fill(`input[name="objectives.${i}.value"]`, testProtocol.objectives[i]);
  }

  // Fill primary endpoints
  for (let i = 0; i < testProtocol.primaryEndpoints.length; i++) {
    if (i > 0) {
      await page.click('button:has-text("Add Primary Endpoint")');
    }
    await page.fill(`input[name="primary_endpoints.${i}.value"]`, testProtocol.primaryEndpoints[i]);
  }

  // Fill secondary endpoints
  for (let i = 0; i < testProtocol.secondaryEndpoints.length; i++) {
    await page.click('button:has-text("Add Secondary Endpoint")');
    await page.fill(`input[name="secondary_endpoints.${i}.value"]`, testProtocol.secondaryEndpoints[i]);
  }
}

async function fillProtocolParticipants(page: Page) {
  // Fill inclusion criteria
  for (let i = 0; i < testProtocol.inclusionCriteria.length; i++) {
    if (i > 0) {
      await page.click('button:has-text("Add Inclusion Criterion")');
    }
    await page.fill(`input[name="inclusion_criteria.${i}.value"]`, testProtocol.inclusionCriteria[i]);
  }

  // Fill exclusion criteria
  for (let i = 0; i < testProtocol.exclusionCriteria.length; i++) {
    if (i > 0) {
      await page.click('button:has-text("Add Exclusion Criterion")');
    }
    await page.fill(`input[name="exclusion_criteria.${i}.value"]`, testProtocol.exclusionCriteria[i]);
  }
}

async function fillProtocolMethodology(page: Page) {
  // Fill supplements
  for (let i = 0; i < testProtocol.supplements.length; i++) {
    if (i > 0) {
      await page.click('button:has-text("Add Supplement")');
    }
    await page.fill(`input[name="supplements.${i}.value"]`, testProtocol.supplements[i]);
  }

  await page.fill('#data_collection_schedule', testProtocol.dataCollection);
}

async function fillProtocolEthics(page: Page) {
  await page.fill('#ethical_considerations', testProtocol.ethicalConsiderations);
  await page.fill('#consent_form_url', testProtocol.consentUrl);
}

test.describe('Research Protocol Management', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/');
  });

  test.describe('Protocol Creation Workflow', () => {
    test('should create a complete research protocol', async ({ page }) => {
      await loginAsResearcher(page);
      await navigateToResearch(page);

      // Start protocol creation
      await page.click('button:has-text("Create Protocol")');
      await expect(page.locator('h2')).toContainText('Basic Information');

      // Step 1: Basic Information
      await fillProtocolBasicInfo(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Objectives & Endpoints');

      // Step 2: Objectives & Endpoints
      await fillProtocolObjectives(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Participant Criteria');

      // Step 3: Participant Criteria
      await fillProtocolParticipants(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Methodology');

      // Step 4: Methodology
      await fillProtocolMethodology(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Ethics & Consent');

      // Step 5: Ethics & Consent
      await fillProtocolEthics(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Review & Submit');

      // Step 6: Review & Submit
      await expect(page.locator('h4')).toContainText(testProtocol.title);
      await expect(page.locator('text=Duration:')).toBeVisible();
      await expect(page.locator('text=16 weeks')).toBeVisible();

      await page.click('button:has-text("Create Protocol")');

      // Verify success
      await expect(page.locator('.notification')).toContainText('Research protocol created successfully');
      await expect(page).toHaveURL(/\/research\/protocols\/\w+/);
    });

    test('should validate required fields in protocol creation', async ({ page }) => {
      await loginAsResearcher(page);
      await navigateToResearch(page);

      await page.click('button:has-text("Create Protocol")');

      // Try to proceed without filling required fields
      await page.click('button:has-text("Next")');

      // Should show validation errors
      await expect(page.locator('text=Title is required')).toBeVisible();
      await expect(page.locator('text=Description is required')).toBeVisible();
      await expect(page.locator('text=Duration is required')).toBeVisible();
    });

    test('should allow navigation between protocol creation steps', async ({ page }) => {
      await loginAsResearcher(page);
      await navigateToResearch(page);

      await page.click('button:has-text("Create Protocol")');

      // Fill basic info and go to next step
      await fillProtocolBasicInfo(page);
      await page.click('button:has-text("Next")');
      await expect(page.locator('h2')).toContainText('Objectives & Endpoints');

      // Go back to previous step
      await page.click('button:has-text("Previous")');
      await expect(page.locator('h2')).toContainText('Basic Information');

      // Verify data is preserved
      await expect(page.locator('#title')).toHaveValue(testProtocol.title);
      await expect(page.locator('#description')).toHaveValue(testProtocol.description);
    });

    test('should handle protocol creation cancellation', async ({ page }) => {
      await loginAsResearcher(page);
      await navigateToResearch(page);

      await page.click('button:has-text("Create Protocol")');
      await fillProtocolBasicInfo(page);

      // Cancel creation
      await page.click('button:has-text("Cancel")');

      // Should return to research page
      await expect(page).toHaveURL('/research');
      await expect(page.locator('h1')).toContainText('Research Hub');
    });
  });

  test.describe('Protocol Discovery and Search', () => {
    test('should display available research protocols', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Should show protocol cards
      await expect(page.locator('[data-testid="protocol-card"]').first()).toBeVisible();
      await expect(page.locator('text=recruiting')).toBeVisible();
      await expect(page.locator('button:has-text("Join Study")')).toBeVisible();
    });

    test('should search protocols by title', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Search for specific protocol
      await page.fill('[placeholder*="Search research protocols"]', 'Vitamin D');
      await page.waitForTimeout(500); // Wait for debounce

      // Should filter results
      await expect(page.locator('text=Vitamin D')).toBeVisible();
      await expect(page.locator('[data-testid="protocol-card"]')).toHaveCount(1);
    });

    test('should filter protocols by status', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Filter by recruiting status
      await page.selectOption('select', 'recruiting');

      // Should show only recruiting protocols
      await expect(page.locator('text=recruiting')).toBeVisible();
      await expect(page.locator('text=completed')).not.toBeVisible();
    });

    test('should clear filters', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Apply filters
      await page.fill('[placeholder*="Search research protocols"]', 'test');
      await page.selectOption('select', 'recruiting');

      // Clear filters
      await page.click('button:has-text("Clear")');

      // Should reset to all protocols
      await expect(page.locator('[placeholder*="Search research protocols"]')).toHaveValue('');
      await expect(page.locator('select')).toHaveValue('');
    });

    test('should show empty state when no protocols match filters', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Search for non-existent protocol
      await page.fill('[placeholder*="Search research protocols"]', 'nonexistent protocol xyz');
      await page.waitForTimeout(500);

      // Should show empty state
      await expect(page.locator('text=No protocols found')).toBeVisible();
      await expect(page.locator('button:has-text("Clear Filters")')).toBeVisible();
    });
  });

  test.describe('Study Participation', () => {
    test('should join a research study', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Find and join a recruiting study
      const protocolCard = page.locator('[data-testid="protocol-card"]').first();
      await protocolCard.locator('button:has-text("Join Study")').click();

      // Should show success notification
      await expect(page.locator('.notification')).toContainText('Successfully joined');

      // Button should change to indicate joined status or be disabled
      await expect(protocolCard.locator('button:has-text("Join Study")')).not.toBeVisible();
    });

    test('should not allow joining full studies', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Find a full study (if any)
      const fullStudyButton = page.locator('button:has-text("Study Full")');
      if (await fullStudyButton.count() > 0) {
        await expect(fullStudyButton.first()).toBeDisabled();
      }
    });

    test('should view protocol details', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Click on protocol card or details button
      await page.locator('[data-testid="protocol-card"]').first().click();

      // Should navigate to protocol details page
      await expect(page).toHaveURL(/\/research\/protocols\/\w+/);
      await expect(page.locator('h1')).toBeVisible();
    });

    test('should handle join study errors gracefully', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Mock API error response
      await page.route('**/research/protocols/*/join', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Already enrolled in this study' })
        });
      });

      // Try to join study
      await page.locator('button:has-text("Join Study")').first().click();

      // Should show error notification
      await expect(page.locator('.notification')).toContainText('Already enrolled in this study');
    });
  });

  test.describe('Mobile Responsiveness', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Should display mobile-friendly layout
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="protocol-card"]')).toBeVisible();

      // Search should work on mobile
      await page.fill('[placeholder*="Search research protocols"]', 'test');
      await page.waitForTimeout(500);

      // Protocol cards should be stacked vertically
      const cards = page.locator('[data-testid="protocol-card"]');
      const firstCard = cards.first();
      const secondCard = cards.nth(1);

      if (await secondCard.count() > 0) {
        const firstCardBox = await firstCard.boundingBox();
        const secondCardBox = await secondCard.boundingBox();
        
        // Second card should be below first card (not side by side)
        expect(secondCardBox!.y).toBeGreaterThan(firstCardBox!.y + firstCardBox!.height);
      }
    });

    test('should handle protocol creation on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsResearcher(page);
      await navigateToResearch(page);

      await page.click('button:has-text("Create Protocol")');

      // Form should be mobile-friendly
      await expect(page.locator('#title')).toBeVisible();
      await page.fill('#title', 'Mobile Test Protocol');

      // Navigation buttons should stack vertically on mobile
      const nextButton = page.locator('button:has-text("Next")');
      await expect(nextButton).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Search input
      await expect(page.locator('[placeholder*="Search research protocols"]')).toBeFocused();

      await page.keyboard.press('Tab'); // Filter select
      await expect(page.locator('select')).toBeFocused();

      await page.keyboard.press('Tab'); // Create button (if visible)
      await page.keyboard.press('Tab'); // First protocol card button

      // Should be able to activate with Enter
      await page.keyboard.press('Enter');
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Check for proper button roles
      await expect(page.locator('button[role="button"]')).toHaveCount(0); // Should not have redundant roles
      await expect(page.locator('button:has-text("Join Study")')).toHaveAttribute('type', 'button');

      // Check for proper form labels
      await expect(page.locator('label[for]')).toHaveCount(0); // In search context
    });

    test('should support screen readers', async ({ page }) => {
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Check for descriptive text and alt attributes
      const protocolCards = page.locator('[data-testid="protocol-card"]');
      await expect(protocolCards.first()).toContainText('recruiting');
      await expect(protocolCards.first()).toContainText('weeks');
      await expect(protocolCards.first()).toContainText('participants');
    });
  });

  test.describe('Performance', () => {
    test('should load research page quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await loginAsParticipant(page);
      await navigateToResearch(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time (adjust threshold as needed)
      expect(loadTime).toBeLessThan(3000);
      
      // Should show content
      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('[data-testid="protocol-card"]')).toBeVisible();
    });

    test('should handle large numbers of protocols efficiently', async ({ page }) => {
      // This would require seeding the database with many protocols
      await loginAsParticipant(page);
      await navigateToResearch(page);

      // Search should be responsive even with many protocols
      const searchStart = Date.now();
      await page.fill('[placeholder*="Search research protocols"]', 'test');
      await page.waitForTimeout(500); // Wait for debounce
      const searchTime = Date.now() - searchStart;

      expect(searchTime).toBeLessThan(1000);
    });
  });
});
