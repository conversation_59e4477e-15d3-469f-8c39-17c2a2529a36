/**
 * Login Page E2E Tests
 * 
 * End-to-end tests for the login workflow using <PERSON>wright.
 */

import { test, expect } from '@playwright/test';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  name: 'Test User',
};

const invalidUser = {
  email: '<EMAIL>',
  password: 'wrongpassword',
};

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page before each test
    await page.goto('/login');
  });

  test.describe('Page Layout and Elements', () => {
    test('should display login form with all required elements', async ({ page }) => {
      // Check page title
      await expect(page).toHaveTitle(/Supplement Tracker/);

      // Check main heading
      await expect(page.getByRole('heading', { name: /supplement tracker/i })).toBeVisible();

      // Check form elements
      await expect(page.getByLabel(/email address/i)).toBeVisible();
      await expect(page.getByLabel(/password/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();

      // Check navigation links
      await expect(page.getByText(/don't have an account/i)).toBeVisible();
      await expect(page.getByRole('link', { name: /create one here/i })).toBeVisible();
      await expect(page.getByRole('link', { name: /forgot your password/i })).toBeVisible();
    });

    test('should have proper branding and description', async ({ page }) => {
      await expect(page.getByText(/evidence-based supplement research platform/i)).toBeVisible();
    });

    test('should focus email field on page load', async ({ page }) => {
      const emailInput = page.getByLabel(/email address/i);
      await expect(emailInput).toBeFocused();
    });
  });

  test.describe('Form Validation', () => {
    test('should show validation errors for empty form submission', async ({ page }) => {
      // Click submit without filling form
      await page.getByRole('button', { name: /sign in/i }).click();

      // Check for validation errors
      await expect(page.getByText(/email is required/i)).toBeVisible();
      await expect(page.getByText(/password is required/i)).toBeVisible();
    });

    test('should validate email format', async ({ page }) => {
      // Enter invalid email
      await page.getByLabel(/email address/i).fill('invalid-email');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Check for email validation error
      await expect(page.getByText(/invalid email address/i)).toBeVisible();
    });

    test('should validate password length', async ({ page }) => {
      // Enter short password
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill('123');
      await page.getByRole('button', { name: /sign in/i }).click();

      // Check for password validation error
      await expect(page.getByText(/password must be at least 6 characters/i)).toBeVisible();
    });

    test('should clear validation errors when corrected', async ({ page }) => {
      // First, trigger validation errors
      await page.getByRole('button', { name: /sign in/i }).click();
      await expect(page.getByText(/email is required/i)).toBeVisible();

      // Then fill in valid data
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill(testUser.password);

      // Validation errors should disappear
      await expect(page.getByText(/email is required/i)).not.toBeVisible();
    });
  });

  test.describe('Form Interaction', () => {
    test('should allow typing in form fields', async ({ page }) => {
      const emailInput = page.getByLabel(/email address/i);
      const passwordInput = page.getByLabel(/password/i);

      // Type in email field
      await emailInput.fill(testUser.email);
      await expect(emailInput).toHaveValue(testUser.email);

      // Type in password field
      await passwordInput.fill(testUser.password);
      await expect(passwordInput).toHaveValue(testUser.password);
    });

    test('should navigate between fields with Tab key', async ({ page }) => {
      const emailInput = page.getByLabel(/email address/i);
      const passwordInput = page.getByLabel(/password/i);
      const submitButton = page.getByRole('button', { name: /sign in/i });

      // Start with email field focused
      await expect(emailInput).toBeFocused();

      // Tab to password field
      await page.keyboard.press('Tab');
      await expect(passwordInput).toBeFocused();

      // Tab to submit button
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab'); // Skip forgot password link
      await expect(submitButton).toBeFocused();
    });

    test('should submit form with Enter key', async ({ page }) => {
      // Fill form
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill(testUser.password);

      // Press Enter to submit
      await page.keyboard.press('Enter');

      // Should attempt to submit (we'll see loading state or error)
      await expect(page.getByRole('button', { name: /sign in/i })).toBeDisabled();
    });
  });

  test.describe('Authentication Flow', () => {
    test('should show loading state during login attempt', async ({ page }) => {
      // Fill form with valid data
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill(testUser.password);

      // Submit form
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should show loading state
      await expect(page.getByTestId('loading-spinner')).toBeVisible();
      await expect(page.getByRole('button', { name: /sign in/i })).toBeDisabled();
    });

    test('should disable form fields during login attempt', async ({ page }) => {
      // Fill form
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill(testUser.password);

      // Submit form
      await page.getByRole('button', { name: /sign in/i }).click();

      // Form fields should be disabled
      await expect(page.getByLabel(/email address/i)).toBeDisabled();
      await expect(page.getByLabel(/password/i)).toBeDisabled();
    });

    test('should display error message for invalid credentials', async ({ page }) => {
      // Mock API response for invalid credentials
      await page.route('**/api/v1/auth/login', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Incorrect email or password',
          }),
        });
      });

      // Fill form with invalid credentials
      await page.getByLabel(/email address/i).fill(invalidUser.email);
      await page.getByLabel(/password/i).fill(invalidUser.password);

      // Submit form
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should show error notification
      await expect(page.getByText(/incorrect email or password/i)).toBeVisible();
    });

    test('should redirect to dashboard on successful login', async ({ page }) => {
      // Mock successful API response
      await page.route('**/api/v1/auth/login', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            access_token: 'mock-token',
            token_type: 'bearer',
            expires_in: 3600,
            user: {
              id: '1',
              email: testUser.email,
              full_name: testUser.name,
              is_active: true,
              is_superuser: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          }),
        });
      });

      // Fill form with valid credentials
      await page.getByLabel(/email address/i).fill(testUser.email);
      await page.getByLabel(/password/i).fill(testUser.password);

      // Submit form
      await page.getByRole('button', { name: /sign in/i }).click();

      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard');
    });
  });

  test.describe('Navigation', () => {
    test('should navigate to registration page', async ({ page }) => {
      await page.getByRole('link', { name: /create one here/i }).click();
      await expect(page).toHaveURL('/register');
    });

    test('should navigate to forgot password page', async ({ page }) => {
      await page.getByRole('link', { name: /forgot your password/i }).click();
      await expect(page).toHaveURL('/forgot-password');
    });
  });

  test.describe('Responsive Design', () => {
    test('should display properly on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });

      // Check that elements are still visible and accessible
      await expect(page.getByRole('heading', { name: /supplement tracker/i })).toBeVisible();
      await expect(page.getByLabel(/email address/i)).toBeVisible();
      await expect(page.getByLabel(/password/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
    });

    test('should display properly on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });

      // Check that elements are still visible and accessible
      await expect(page.getByRole('heading', { name: /supplement tracker/i })).toBeVisible();
      await expect(page.getByLabel(/email address/i)).toBeVisible();
      await expect(page.getByLabel(/password/i)).toBeVisible();
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper ARIA labels and roles', async ({ page }) => {
      // Check form has proper role
      const form = page.locator('form');
      await expect(form).toBeVisible();

      // Check inputs have proper labels
      await expect(page.getByLabel(/email address/i)).toHaveAttribute('type', 'email');
      await expect(page.getByLabel(/password/i)).toHaveAttribute('type', 'password');
    });

    test('should be navigable with keyboard only', async ({ page }) => {
      // Start navigation with Tab
      await page.keyboard.press('Tab');
      await expect(page.getByLabel(/email address/i)).toBeFocused();

      // Continue tabbing through form
      await page.keyboard.press('Tab');
      await expect(page.getByLabel(/password/i)).toBeFocused();

      // Should be able to reach all interactive elements
      await page.keyboard.press('Tab');
      await expect(page.getByRole('link', { name: /forgot your password/i })).toBeFocused();
    });
  });
});
