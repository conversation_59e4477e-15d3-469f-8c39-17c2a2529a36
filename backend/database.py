"""
Database configuration and models for Supplement Tracker
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Float, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from datetime import datetime
import os

# Database URL - using SQLite for development
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./supplement_tracker.db")

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class User(Base):
    """User model for authentication and profile management"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    email_verified = Column(Boolean, default=False)
    email_verification_token = Column(String, nullable=True)
    password_reset_token = Column(String, nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    last_login = Column(DateTime, nullable=True)
    login_count = Column(Integer, default=0)
    timezone = Column(String, default="UTC")
    date_format = Column(String, default="YYYY-MM-DD")
    profile_picture = Column(String, nullable=True)
    bio = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    supplements = relationship("UserSupplement", back_populates="user")
    intake_logs = relationship("IntakeLog", back_populates="user")
    research_participations = relationship("ResearchParticipation", back_populates="user")
    health_metrics = relationship("HealthMetric", back_populates="user")

class Supplement(Base):
    """Master supplement catalog"""
    __tablename__ = "supplements"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    brand = Column(String)
    category = Column(String, index=True)
    description = Column(Text)
    default_dosage = Column(String)
    dosage_unit = Column(String)
    warnings = Column(Text)
    interactions = Column(Text)
    evidence_level = Column(String)  # high, medium, low
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    user_supplements = relationship("UserSupplement", back_populates="supplement")

class UserSupplement(Base):
    """User's personal supplement schedule"""
    __tablename__ = "user_supplements"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    supplement_id = Column(Integer, ForeignKey("supplements.id"), nullable=False)
    custom_name = Column(String)  # User's custom name for the supplement
    dosage = Column(String, nullable=False)
    frequency = Column(String, nullable=False)  # daily, twice_daily, weekly, etc.
    time_of_day = Column(String)  # morning, evening, with_meals, etc.
    start_date = Column(DateTime, default=func.now())
    end_date = Column(DateTime)
    is_active = Column(Boolean, default=True)
    notes = Column(Text)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="supplements")
    supplement = relationship("Supplement", back_populates="user_supplements")
    intake_logs = relationship("IntakeLog", back_populates="user_supplement")

class IntakeLog(Base):
    """Daily supplement intake tracking"""
    __tablename__ = "intake_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user_supplement_id = Column(Integer, ForeignKey("user_supplements.id"), nullable=False)
    taken_at = Column(DateTime, nullable=False)
    dosage_taken = Column(String)
    notes = Column(Text)
    mood_before = Column(Integer)  # 1-10 scale
    mood_after = Column(Integer)   # 1-10 scale
    side_effects = Column(Text)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="intake_logs")
    user_supplement = relationship("UserSupplement", back_populates="intake_logs")

class ResearchStudy(Base):
    """Research studies available for participation"""
    __tablename__ = "research_studies"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String, index=True)
    duration_weeks = Column(Integer)
    max_participants = Column(Integer)
    current_participants = Column(Integer, default=0)
    status = Column(String, default="recruiting")  # recruiting, active, completed
    difficulty = Column(String)  # beginner, intermediate, advanced
    compensation = Column(String)
    requirements = Column(Text)  # JSON string of requirements
    researcher_name = Column(String)
    institution = Column(String)
    start_date = Column(DateTime)
    estimated_completion = Column(DateTime)
    rating = Column(Float, default=0.0)
    rating_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    participations = relationship("ResearchParticipation", back_populates="study")

class ResearchParticipation(Base):
    """User participation in research studies"""
    __tablename__ = "research_participations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    study_id = Column(Integer, ForeignKey("research_studies.id"), nullable=False)
    joined_at = Column(DateTime, default=func.now())
    status = Column(String, default="active")  # active, completed, withdrawn
    progress_percentage = Column(Float, default=0.0)
    next_task = Column(String)
    next_task_due = Column(DateTime)
    completion_date = Column(DateTime)
    rating = Column(Integer)  # User's rating of the study
    feedback = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="research_participations")
    study = relationship("ResearchStudy", back_populates="participations")

class HealthMetric(Base):
    """User health metrics and biomarkers"""
    __tablename__ = "health_metrics"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    metric_type = Column(String, nullable=False)  # weight, sleep_hours, energy_level, etc.
    value = Column(Float, nullable=False)
    unit = Column(String)
    recorded_at = Column(DateTime, nullable=False)
    notes = Column(Text)
    source = Column(String)  # manual, device, app
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User", back_populates="health_metrics")

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create tables
def create_tables():
    Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    create_tables()
    print("Database tables created successfully!")
