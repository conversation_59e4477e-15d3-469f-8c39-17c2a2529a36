"""
Supplement management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database import get_db, User, Supplement, UserSupplement, IntakeLog

router = APIRouter(prefix="/supplements", tags=["supplements"])

# Pydantic models for request/response
class SupplementBase(BaseModel):
    name: str
    brand: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    default_dosage: Optional[str] = None
    dosage_unit: Optional[str] = None

class SupplementCreate(SupplementBase):
    pass

class SupplementResponse(SupplementBase):
    id: int
    evidence_level: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserSupplementBase(BaseModel):
    supplement_id: int
    custom_name: Optional[str] = None
    dosage: str
    frequency: str
    time_of_day: Optional[str] = None
    notes: Optional[str] = None

class UserSupplementCreate(UserSupplementBase):
    pass

class UserSupplementResponse(UserSupplementBase):
    id: int
    user_id: int
    is_active: bool
    start_date: datetime
    supplement: SupplementResponse
    
    class Config:
        from_attributes = True

class IntakeLogBase(BaseModel):
    user_supplement_id: int
    taken_at: datetime
    dosage_taken: Optional[str] = None
    notes: Optional[str] = None
    mood_before: Optional[int] = None
    mood_after: Optional[int] = None

class IntakeLogCreate(IntakeLogBase):
    pass

class IntakeLogResponse(IntakeLogBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    today_intakes: int
    active_supplements: int
    current_streak: int
    weekly_compliance: float

# Import authentication
from auth import get_current_active_user

# Supplement catalog endpoints
@router.get("/catalog", response_model=List[SupplementResponse])
async def get_supplement_catalog(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get supplement catalog with search and filtering"""
    query = db.query(Supplement)

    if search:
        query = query.filter(
            Supplement.name.ilike(f"%{search}%") |
            Supplement.brand.ilike(f"%{search}%") |
            Supplement.description.ilike(f"%{search}%")
        )

    if category:
        query = query.filter(Supplement.category == category)

    supplements = query.offset(skip).limit(limit).all()
    return supplements

@router.get("/catalog/{supplement_id}", response_model=SupplementResponse)
async def get_supplement_details(
    supplement_id: int,
    db: Session = Depends(get_db)
):
    """Get specific supplement details"""
    supplement = db.query(Supplement).filter(Supplement.id == supplement_id).first()

    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    return supplement

@router.post("/catalog", response_model=SupplementResponse, status_code=status.HTTP_201_CREATED)
async def create_supplement(
    supplement_data: SupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new supplement in the catalog"""
    # Check if supplement already exists
    existing = db.query(Supplement).filter(
        (Supplement.name == supplement_data.name) &
        (Supplement.brand == supplement_data.brand)
    ).first()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement with this name and brand already exists"
        )

    supplement = Supplement(**supplement_data.dict())
    db.add(supplement)
    db.commit()
    db.refresh(supplement)

    return supplement

# User supplement management
@router.get("/my-supplements", response_model=List[UserSupplementResponse])
async def get_my_supplements(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's supplement schedule"""
    query = db.query(UserSupplement).filter(UserSupplement.user_id == current_user.id)

    if active_only:
        query = query.filter(UserSupplement.is_active == True)

    user_supplements = query.all()
    return user_supplements

@router.post("/my-supplements", response_model=UserSupplementResponse, status_code=status.HTTP_201_CREATED)
async def add_supplement_to_schedule(
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Add a supplement to user's schedule"""
    # Verify supplement exists
    supplement = db.query(Supplement).filter(Supplement.id == supplement_data.supplement_id).first()
    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    # Check if user already has this supplement in their schedule
    existing = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.supplement_id == supplement_data.supplement_id) &
        (UserSupplement.is_active == True)
    ).first()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement already in your active schedule"
        )

    user_supplement = UserSupplement(
        user_id=current_user.id,
        **supplement_data.dict()
    )

    db.add(user_supplement)
    db.commit()
    db.refresh(user_supplement)

    return user_supplement

@router.put("/my-supplements/{user_supplement_id}", response_model=UserSupplementResponse)
async def update_supplement_schedule(
    user_supplement_id: int,
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update user's supplement schedule"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()

    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )

    for field, value in supplement_data.dict(exclude_unset=True).items():
        setattr(user_supplement, field, value)

    db.commit()
    db.refresh(user_supplement)

    return user_supplement

@router.delete("/my-supplements/{user_supplement_id}")
async def remove_supplement_from_schedule(
    user_supplement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Remove supplement from user's schedule (soft delete)"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()

    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )

    user_supplement.is_active = False
    db.commit()

    return {"message": "Supplement removed from schedule"}

# Intake tracking
@router.post("/intake", response_model=IntakeLogResponse, status_code=status.HTTP_201_CREATED)
async def log_supplement_intake(
    intake_data: IntakeLogCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Log supplement intake"""
    # Verify user supplement exists and belongs to current user
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == intake_data.user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()

    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )

    intake_log = IntakeLog(
        user_id=current_user.id,
        **intake_data.dict()
    )

    db.add(intake_log)
    db.commit()
    db.refresh(intake_log)

    return intake_log

@router.get("/intake/history", response_model=List[IntakeLogResponse])
async def get_intake_history(
    skip: int = 0,
    limit: int = 100,
    supplement_id: Optional[int] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's supplement intake history"""
    query = db.query(IntakeLog).filter(IntakeLog.user_id == current_user.id)

    if supplement_id:
        query = query.join(UserSupplement).filter(UserSupplement.supplement_id == supplement_id)

    if start_date:
        query = query.filter(IntakeLog.taken_at >= start_date)

    if end_date:
        query = query.filter(IntakeLog.taken_at <= end_date)

    intake_logs = query.order_by(IntakeLog.taken_at.desc()).offset(skip).limit(limit).all()
    return intake_logs

@router.get("/categories")
async def get_supplement_categories(db: Session = Depends(get_db)):
    """Get list of supplement categories"""
    categories = db.query(Supplement.category).distinct().all()
    return {"categories": [cat[0] for cat in categories if cat[0]]}

@router.get("/stats")
async def get_supplement_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's supplement statistics"""
    # Count active supplements
    active_supplements = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.is_active == True)
    ).count()

    # Count total intake logs
    total_intakes = db.query(IntakeLog).filter(IntakeLog.user_id == current_user.id).count()

    # Count intakes this week
    from datetime import timedelta
    week_ago = datetime.utcnow() - timedelta(days=7)
    weekly_intakes = db.query(IntakeLog).filter(
        (IntakeLog.user_id == current_user.id) &
        (IntakeLog.taken_at >= week_ago)
    ).count()

    return {
        "active_supplements": active_supplements,
        "total_intakes": total_intakes,
        "weekly_intakes": weekly_intakes,
        "adherence_rate": round((weekly_intakes / (active_supplements * 7)) * 100, 1) if active_supplements > 0 else 0
    }

@router.get("/catalog", response_model=List[SupplementResponse])
async def get_supplement_catalog(
    category: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get supplement catalog with optional filtering"""
    query = db.query(Supplement)
    
    if category:
        query = query.filter(Supplement.category == category)
    
    if search:
        query = query.filter(Supplement.name.contains(search))
    
    supplements = query.all()
    return supplements

@router.post("/catalog", response_model=SupplementResponse)
async def create_supplement(
    supplement: SupplementCreate,
    db: Session = Depends(get_db)
):
    """Create a new supplement in the catalog"""
    db_supplement = Supplement(**supplement.dict())
    db.add(db_supplement)
    db.commit()
    db.refresh(db_supplement)
    return db_supplement

@router.get("/my-supplements", response_model=List[UserSupplementResponse])
async def get_user_supplements(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get user's personal supplement schedule"""
    query = db.query(UserSupplement).filter(UserSupplement.user_id == current_user["id"])
    
    if active_only:
        query = query.filter(UserSupplement.is_active == True)
    
    user_supplements = query.all()
    return user_supplements

@router.post("/my-supplements", response_model=UserSupplementResponse)
async def add_user_supplement(
    user_supplement: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add a supplement to user's schedule"""
    db_user_supplement = UserSupplement(
        user_id=current_user["id"],
        **user_supplement.dict()
    )
    db.add(db_user_supplement)
    db.commit()
    db.refresh(db_user_supplement)
    return db_user_supplement

@router.put("/my-supplements/{supplement_id}")
async def update_user_supplement(
    supplement_id: int,
    user_supplement: UserSupplementBase,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update user's supplement schedule"""
    db_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not db_supplement:
        raise HTTPException(status_code=404, detail="Supplement not found")
    
    for key, value in user_supplement.dict(exclude_unset=True).items():
        setattr(db_supplement, key, value)
    
    db.commit()
    db.refresh(db_supplement)
    return db_supplement

@router.delete("/my-supplements/{supplement_id}")
async def remove_user_supplement(
    supplement_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Remove supplement from user's schedule"""
    db_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not db_supplement:
        raise HTTPException(status_code=404, detail="Supplement not found")
    
    db_supplement.is_active = False
    db.commit()
    return {"message": "Supplement removed successfully"}

@router.post("/intake", response_model=IntakeLogResponse)
async def log_intake(
    intake: IntakeLogCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Log supplement intake"""
    # Verify the user supplement belongs to the current user
    user_supplement = db.query(UserSupplement).filter(
        UserSupplement.id == intake.user_supplement_id,
        UserSupplement.user_id == current_user["id"]
    ).first()
    
    if not user_supplement:
        raise HTTPException(status_code=404, detail="User supplement not found")
    
    db_intake = IntakeLog(
        user_id=current_user["id"],
        **intake.dict()
    )
    db.add(db_intake)
    db.commit()
    db.refresh(db_intake)
    return db_intake

@router.get("/intake/today", response_model=List[IntakeLogResponse])
async def get_today_intake(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get today's intake logs"""
    today = date.today()
    intake_logs = db.query(IntakeLog).filter(
        IntakeLog.user_id == current_user["id"],
        IntakeLog.taken_at >= today
    ).all()
    return intake_logs

@router.get("/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get dashboard statistics"""
    today = date.today()
    
    # Today's intakes
    today_intakes = db.query(IntakeLog).filter(
        IntakeLog.user_id == current_user["id"],
        IntakeLog.taken_at >= today
    ).count()
    
    # Active supplements
    active_supplements = db.query(UserSupplement).filter(
        UserSupplement.user_id == current_user["id"],
        UserSupplement.is_active == True
    ).count()
    
    # Mock data for now (implement real calculations later)
    current_streak = 15
    weekly_compliance = 85.0
    
    return DashboardStats(
        today_intakes=today_intakes,
        active_supplements=active_supplements,
        current_streak=current_streak,
        weekly_compliance=weekly_compliance
    )

@router.get("/due-today")
async def get_supplements_due_today(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get supplements due today that haven't been taken"""
    today = date.today()
    
    # Get all active user supplements
    user_supplements = db.query(UserSupplement).filter(
        UserSupplement.user_id == current_user["id"],
        UserSupplement.is_active == True
    ).all()
    
    # Check which ones haven't been taken today
    due_supplements = []
    for user_supplement in user_supplements:
        today_intake = db.query(IntakeLog).filter(
            IntakeLog.user_supplement_id == user_supplement.id,
            IntakeLog.taken_at >= today
        ).first()
        
        if not today_intake:
            due_supplements.append({
                "id": user_supplement.id,
                "name": user_supplement.custom_name or user_supplement.supplement.name,
                "brand": user_supplement.supplement.brand,
                "dosage": user_supplement.dosage,
                "time_of_day": user_supplement.time_of_day,
                "frequency": user_supplement.frequency
            })
    
    return due_supplements
