"""
Research study management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from database import get_db, ResearchStudy, ResearchParticipation

router = APIRouter(prefix="/research", tags=["research"])

# Pydantic models
class ResearchStudyBase(BaseModel):
    title: str
    description: str
    category: Optional[str] = None
    duration_weeks: Optional[int] = None
    max_participants: Optional[int] = None
    difficulty: Optional[str] = "beginner"
    compensation: Optional[str] = None
    requirements: Optional[str] = None
    researcher_name: Optional[str] = None
    institution: Optional[str] = None

class ResearchStudyCreate(ResearchStudyBase):
    pass

class ResearchStudyResponse(ResearchStudyBase):
    id: int
    current_participants: int
    status: str
    rating: float
    rating_count: int
    start_date: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class ResearchParticipationBase(BaseModel):
    study_id: int

class ResearchParticipationCreate(ResearchParticipationBase):
    pass

class ResearchParticipationResponse(ResearchParticipationBase):
    id: int
    user_id: int
    joined_at: datetime
    status: str
    progress_percentage: float
    next_task: Optional[str] = None
    next_task_due: Optional[datetime] = None
    study: ResearchStudyResponse
    
    class Config:
        from_attributes = True

class StudyRating(BaseModel):
    rating: int
    feedback: Optional[str] = None

# Mock user for development
def get_current_user():
    return {"id": 1, "username": "demo_user", "email": "<EMAIL>"}

@router.get("/studies", response_model=List[ResearchStudyResponse])
async def get_research_studies(
    category: Optional[str] = None,
    status: Optional[str] = None,
    difficulty: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get available research studies with filtering"""
    query = db.query(ResearchStudy)
    
    if category:
        query = query.filter(ResearchStudy.category == category)
    
    if status:
        query = query.filter(ResearchStudy.status == status)
    
    if difficulty:
        query = query.filter(ResearchStudy.difficulty == difficulty)
    
    if search:
        query = query.filter(ResearchStudy.title.contains(search))
    
    studies = query.all()
    return studies

@router.get("/studies/{study_id}", response_model=ResearchStudyResponse)
async def get_research_study(
    study_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific study"""
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if not study:
        raise HTTPException(status_code=404, detail="Study not found")
    return study

@router.post("/studies", response_model=ResearchStudyResponse)
async def create_research_study(
    study: ResearchStudyCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new research study"""
    db_study = ResearchStudy(**study.dict())
    db.add(db_study)
    db.commit()
    db.refresh(db_study)
    return db_study

@router.post("/studies/{study_id}/join", response_model=ResearchParticipationResponse)
async def join_study(
    study_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Join a research study"""
    # Check if study exists
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if not study:
        raise HTTPException(status_code=404, detail="Study not found")
    
    # Check if user is already participating
    existing_participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()
    
    if existing_participation:
        raise HTTPException(status_code=400, detail="Already participating in this study")
    
    # Check if study has space
    if study.current_participants >= study.max_participants:
        raise HTTPException(status_code=400, detail="Study is full")
    
    # Create participation record
    participation = ResearchParticipation(
        user_id=current_user["id"],
        study_id=study_id,
        next_task="Complete initial survey",
        next_task_due=datetime.now().replace(hour=23, minute=59, second=59)
    )
    
    db.add(participation)
    
    # Update participant count
    study.current_participants += 1
    
    db.commit()
    db.refresh(participation)
    return participation

@router.post("/studies/{study_id}/leave")
async def leave_study(
    study_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Leave a research study"""
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()
    
    if not participation:
        raise HTTPException(status_code=404, detail="Not participating in this study")
    
    participation.status = "withdrawn"
    
    # Update participant count
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if study:
        study.current_participants = max(0, study.current_participants - 1)
    
    db.commit()
    return {"message": "Successfully left the study"}

@router.get("/my-participations", response_model=List[ResearchParticipationResponse])
async def get_my_participations(
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get user's research participations"""
    query = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"]
    )
    
    if status:
        query = query.filter(ResearchParticipation.status == status)
    
    participations = query.all()
    return participations

@router.put("/participations/{participation_id}/progress")
async def update_participation_progress(
    participation_id: int,
    progress_percentage: float,
    next_task: Optional[str] = None,
    next_task_due: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update participation progress"""
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.id == participation_id,
        ResearchParticipation.user_id == current_user["id"]
    ).first()
    
    if not participation:
        raise HTTPException(status_code=404, detail="Participation not found")
    
    participation.progress_percentage = min(100.0, max(0.0, progress_percentage))
    
    if next_task:
        participation.next_task = next_task
    
    if next_task_due:
        participation.next_task_due = next_task_due
    
    # Mark as completed if 100%
    if participation.progress_percentage >= 100.0:
        participation.status = "completed"
        participation.completion_date = datetime.now()
    
    db.commit()
    db.refresh(participation)
    return participation

@router.post("/studies/{study_id}/rate")
async def rate_study(
    study_id: int,
    rating_data: StudyRating,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Rate a completed study"""
    # Check if user participated in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "completed"
    ).first()
    
    if not participation:
        raise HTTPException(status_code=400, detail="Can only rate completed studies")
    
    # Update participation with rating
    participation.rating = rating_data.rating
    participation.feedback = rating_data.feedback
    
    # Update study's overall rating
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()
    if study:
        # Calculate new average rating
        total_rating = study.rating * study.rating_count + rating_data.rating
        study.rating_count += 1
        study.rating = total_rating / study.rating_count
    
    db.commit()
    return {"message": "Rating submitted successfully"}

@router.get("/dashboard/stats")
async def get_research_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get research dashboard statistics"""
    # Active participations
    active_participations = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.status == "active"
    ).count()
    
    # Completed studies
    completed_studies = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.status == "completed"
    ).count()
    
    # Available studies
    available_studies = db.query(ResearchStudy).filter(
        ResearchStudy.status == "recruiting"
    ).count()
    
    return {
        "active_participations": active_participations,
        "completed_studies": completed_studies,
        "available_studies": available_studies,
        "research_points": completed_studies * 100  # Mock points system
    }

# Data Collection Endpoints
class DataCollectionEntry(BaseModel):
    study_id: int
    data_type: str  # 'supplement_intake', 'mood', 'side_effects', 'biomarker', 'survey'
    data_value: str  # JSON string containing the actual data
    notes: Optional[str] = None

class DataCollectionResponse(BaseModel):
    id: int
    study_id: int
    user_id: int
    data_type: str
    data_value: str
    notes: Optional[str] = None
    collected_at: datetime

    class Config:
        from_attributes = True

@router.post("/studies/{study_id}/data", response_model=DataCollectionResponse)
async def submit_research_data(
    study_id: int,
    data_entry: DataCollectionEntry,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Submit research data for a study"""
    # Verify user is participating in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()

    if not participation:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not participating in this study"
        )

    # Create data collection record (would need to add this model to database.py)
    # For now, return a mock response
    return {
        "id": 1,
        "study_id": study_id,
        "user_id": current_user["id"],
        "data_type": data_entry.data_type,
        "data_value": data_entry.data_value,
        "notes": data_entry.notes,
        "collected_at": datetime.now()
    }

@router.get("/studies/{study_id}/data", response_model=List[DataCollectionResponse])
async def get_research_data(
    study_id: int,
    data_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get research data for a study (participant's own data)"""
    # Verify user is participating in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id
    ).first()

    if not participation:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not participating in this study"
        )

    # Return mock data for now
    return []

@router.get("/studies/{study_id}/forms")
async def get_study_forms(
    study_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get data collection forms for a study"""
    # Verify user is participating in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()

    if not participation:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not participating in this study"
        )

    # Return mock forms based on study type
    study = db.query(ResearchStudy).filter(ResearchStudy.id == study_id).first()

    forms = []
    if study:
        if "vitamin" in study.title.lower() or "supplement" in study.title.lower():
            forms.extend([
                {
                    "id": "daily_intake",
                    "title": "Daily Supplement Intake",
                    "description": "Log your daily supplement intake",
                    "fields": [
                        {"name": "supplement_taken", "type": "boolean", "label": "Did you take your supplement today?"},
                        {"name": "time_taken", "type": "time", "label": "What time did you take it?"},
                        {"name": "with_food", "type": "boolean", "label": "Did you take it with food?"},
                        {"name": "side_effects", "type": "text", "label": "Any side effects?"}
                    ]
                },
                {
                    "id": "weekly_mood",
                    "title": "Weekly Mood Assessment",
                    "description": "Rate your mood and energy levels",
                    "fields": [
                        {"name": "mood_rating", "type": "scale", "label": "Overall mood (1-10)", "min": 1, "max": 10},
                        {"name": "energy_level", "type": "scale", "label": "Energy level (1-10)", "min": 1, "max": 10},
                        {"name": "sleep_quality", "type": "scale", "label": "Sleep quality (1-10)", "min": 1, "max": 10},
                        {"name": "notes", "type": "textarea", "label": "Additional notes"}
                    ]
                }
            ])

        if "cognitive" in study.title.lower() or "brain" in study.title.lower():
            forms.append({
                "id": "cognitive_assessment",
                "title": "Cognitive Performance Test",
                "description": "Complete cognitive performance tasks",
                "fields": [
                    {"name": "memory_test_score", "type": "number", "label": "Memory test score"},
                    {"name": "attention_test_score", "type": "number", "label": "Attention test score"},
                    {"name": "reaction_time", "type": "number", "label": "Average reaction time (ms)"},
                    {"name": "difficulty_rating", "type": "scale", "label": "Test difficulty (1-5)", "min": 1, "max": 5}
                ]
            })

    return {
        "study_id": study_id,
        "forms": forms,
        "next_due": participation.next_task_due,
        "progress": participation.progress_percentage
    }

@router.post("/studies/{study_id}/forms/{form_id}/submit")
async def submit_study_form(
    study_id: int,
    form_id: str,
    form_data: dict,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Submit a completed study form"""
    # Verify user is participating in the study
    participation = db.query(ResearchParticipation).filter(
        ResearchParticipation.user_id == current_user["id"],
        ResearchParticipation.study_id == study_id,
        ResearchParticipation.status == "active"
    ).first()

    if not participation:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not participating in this study"
        )

    # Process form submission (would store in data collection table)
    # Update participation progress
    if form_id == "daily_intake":
        participation.progress_percentage = min(100.0, participation.progress_percentage + 2.0)
    elif form_id == "weekly_mood":
        participation.progress_percentage = min(100.0, participation.progress_percentage + 10.0)
    elif form_id == "cognitive_assessment":
        participation.progress_percentage = min(100.0, participation.progress_percentage + 15.0)

    # Update next task
    if participation.progress_percentage < 100.0:
        participation.next_task = "Complete next scheduled assessment"
        participation.next_task_due = datetime.now().replace(hour=23, minute=59, second=59)
    else:
        participation.next_task = "Study completed - please rate your experience"
        participation.status = "completed"
        participation.completion_date = datetime.now()

    db.commit()

    return {
        "message": "Form submitted successfully",
        "progress": participation.progress_percentage,
        "next_task": participation.next_task,
        "next_due": participation.next_task_due
    }
