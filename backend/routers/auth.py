"""
Authentication Router

Handles user registration, login, password reset, and profile management.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import and_

from database import get_db, User
from auth import (
    authenticate_user, create_access_token, create_refresh_token,
    get_password_hash, verify_token, get_current_user, get_current_active_user,
    UserLogin, UserRegister, UserProfile, UserUpdate, Token, PasswordReset, PasswordResetConfirm
)

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=UserProfile, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserRegister, db: Session = Depends(get_db)):
    """Register a new user"""
    
    # Validate passwords
    try:
        user_data.validate_passwords()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # Check if user already exists
    existing_user = db.query(User).filter(
        (User.email == user_data.email) | (User.username == user_data.email)
    ).first()
    
    if existing_user:
        raise HTTPException(
            status_code=400,
            detail="User with this email already exists"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    
    # Generate username from email if not provided
    username = user_data.email.split('@')[0]
    counter = 1
    original_username = username
    
    # Ensure username is unique
    while db.query(User).filter(User.username == username).first():
        username = f"{original_username}{counter}"
        counter += 1
    
    db_user = User(
        email=user_data.email,
        username=username,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        is_active=True,
        email_verified=False  # Will be verified via email
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return UserProfile.from_orm(db_user)

@router.post("/login", response_model=Token)
async def login_user(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login user with email/username and password"""
    
    # Authenticate user (form_data.username can be email or username)
    user = authenticate_user(db, form_data.username, form_data.password)
    
    if not user:
        # Try with username if email didn't work
        user = db.query(User).filter(User.username == form_data.username).first()
        if user and not authenticate_user(db, user.email, form_data.password):
            user = None
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email/username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is disabled",
        )
    
    # Update login info
    user.last_login = datetime.utcnow()
    user.login_count += 1
    db.commit()
    
    # Create tokens
    access_token = create_access_token(data={"sub": str(user.id), "email": user.email})
    refresh_token = create_refresh_token(data={"sub": str(user.id), "email": user.email})
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@router.post("/refresh", response_model=Token)
async def refresh_token(refresh_token: str, db: Session = Depends(get_db)):
    """Refresh access token using refresh token"""
    
    try:
        payload = verify_token(refresh_token, "refresh")
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid refresh token")
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(status_code=401, detail="User not found or inactive")
        
        # Create new tokens
        access_token = create_access_token(data={"sub": str(user.id), "email": user.email})
        new_refresh_token = create_refresh_token(data={"sub": str(user.id), "email": user.email})
        
        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,
            token_type="bearer"
        )
        
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid refresh token")

@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(current_user: User = Depends(get_current_active_user)):
    """Get current user profile"""
    return UserProfile.from_orm(current_user)

@router.put("/me", response_model=UserProfile)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user profile"""
    
    # Handle password change
    if user_update.new_password:
        try:
            user_update.validate_password_change()
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        
        # Verify current password
        if not authenticate_user(db, current_user.email, user_update.current_password):
            raise HTTPException(status_code=400, detail="Current password is incorrect")
        
        # Update password
        current_user.hashed_password = get_password_hash(user_update.new_password)
    
    # Update other fields
    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name
    
    if user_update.email is not None and user_update.email != current_user.email:
        # Check if email is already taken
        existing_user = db.query(User).filter(
            and_(User.email == user_update.email, User.id != current_user.id)
        ).first()
        
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already taken")
        
        current_user.email = user_update.email
        current_user.email_verified = False  # Require re-verification
    
    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)
    
    return UserProfile.from_orm(current_user)

@router.post("/password-reset")
async def request_password_reset(reset_data: PasswordReset, db: Session = Depends(get_db)):
    """Request password reset email"""
    
    user = db.query(User).filter(User.email == reset_data.email).first()
    
    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists, a password reset link has been sent"}
    
    # Generate reset token (in production, send this via email)
    reset_token = create_access_token(
        data={"sub": str(user.id), "type": "password_reset"},
        expires_delta=timedelta(hours=1)
    )
    
    user.password_reset_token = reset_token
    user.password_reset_expires = datetime.utcnow() + timedelta(hours=1)
    db.commit()
    
    # In production, send email here
    # For demo, return the token (remove this in production)
    return {
        "message": "Password reset link sent to email",
        "reset_token": reset_token  # Remove this in production
    }

@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """Confirm password reset with token"""
    
    try:
        reset_data.validate_passwords()
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    try:
        payload = verify_token(reset_data.token, "access")
        user_id = payload.get("sub")
        
        if user_id is None or payload.get("type") != "password_reset":
            raise HTTPException(status_code=400, detail="Invalid reset token")
        
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user or user.password_reset_token != reset_data.token:
            raise HTTPException(status_code=400, detail="Invalid reset token")
        
        if user.password_reset_expires and user.password_reset_expires < datetime.utcnow():
            raise HTTPException(status_code=400, detail="Reset token has expired")
        
        # Update password
        user.hashed_password = get_password_hash(reset_data.new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        user.updated_at = datetime.utcnow()
        
        db.commit()
        
        return {"message": "Password reset successful"}
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=400, detail="Invalid reset token")

@router.post("/logout")
async def logout_user(current_user: User = Depends(get_current_active_user)):
    """Logout user (client should discard tokens)"""
    # In a production app, you might want to blacklist the token
    # For now, we just return a success message
    return {"message": "Successfully logged out"}

@router.delete("/me")
async def delete_account(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete user account (soft delete by deactivating)"""
    
    current_user.is_active = False
    current_user.updated_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Account deactivated successfully"}
