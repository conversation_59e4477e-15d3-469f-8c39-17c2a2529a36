"""
Supplement management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database import get_db, User, Supplement, UserSupplement, IntakeLog
from auth import get_current_active_user

router = APIRouter(prefix="/supplements", tags=["supplements"])

# Pydantic models for request/response
class SupplementBase(BaseModel):
    name: str
    brand: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    default_dosage: Optional[str] = None
    dosage_unit: Optional[str] = None

class SupplementCreate(SupplementBase):
    pass

class SupplementResponse(SupplementBase):
    id: int
    evidence_level: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserSupplementBase(BaseModel):
    supplement_id: int
    custom_name: Optional[str] = None
    dosage: str
    frequency: str
    time_of_day: Optional[str] = None
    notes: Optional[str] = None

class UserSupplementCreate(UserSupplementBase):
    pass

class UserSupplementResponse(UserSupplementBase):
    id: int
    user_id: int
    is_active: bool
    start_date: datetime
    supplement: SupplementResponse
    
    class Config:
        from_attributes = True

class IntakeLogBase(BaseModel):
    user_supplement_id: int
    taken_at: datetime
    dosage_taken: Optional[str] = None
    notes: Optional[str] = None
    mood_before: Optional[int] = None
    mood_after: Optional[int] = None

class IntakeLogCreate(IntakeLogBase):
    pass

class IntakeLogResponse(IntakeLogBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    today_intakes: int
    active_supplements: int
    current_streak: int
    weekly_compliance: float

# Supplement catalog endpoints
@router.get("/catalog", response_model=List[SupplementResponse])
async def get_supplement_catalog(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get supplement catalog with search and filtering"""
    query = db.query(Supplement)
    
    if search:
        query = query.filter(
            Supplement.name.ilike(f"%{search}%") |
            Supplement.brand.ilike(f"%{search}%") |
            Supplement.description.ilike(f"%{search}%")
        )
    
    if category:
        query = query.filter(Supplement.category == category)
    
    supplements = query.offset(skip).limit(limit).all()
    return supplements

@router.get("/catalog/{supplement_id}", response_model=SupplementResponse)
async def get_supplement_details(
    supplement_id: int,
    db: Session = Depends(get_db)
):
    """Get specific supplement details"""
    supplement = db.query(Supplement).filter(Supplement.id == supplement_id).first()
    
    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )
    
    return supplement

@router.post("/catalog", response_model=SupplementResponse, status_code=status.HTTP_201_CREATED)
async def create_supplement(
    supplement_data: SupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new supplement in the catalog"""
    # Check if supplement already exists
    existing = db.query(Supplement).filter(
        (Supplement.name == supplement_data.name) &
        (Supplement.brand == supplement_data.brand)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement with this name and brand already exists"
        )
    
    supplement = Supplement(**supplement_data.dict())
    db.add(supplement)
    db.commit()
    db.refresh(supplement)
    
    return supplement

# User supplement management
@router.get("/my-supplements", response_model=List[UserSupplementResponse])
async def get_my_supplements(
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's supplement schedule"""
    query = db.query(UserSupplement).filter(UserSupplement.user_id == current_user.id)
    
    if active_only:
        query = query.filter(UserSupplement.is_active == True)
    
    user_supplements = query.all()
    return user_supplements

@router.post("/my-supplements", response_model=UserSupplementResponse, status_code=status.HTTP_201_CREATED)
async def add_supplement_to_schedule(
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Add a supplement to user's schedule"""
    # Verify supplement exists
    supplement = db.query(Supplement).filter(Supplement.id == supplement_data.supplement_id).first()
    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )
    
    # Check if user already has this supplement in their schedule
    existing = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.supplement_id == supplement_data.supplement_id) &
        (UserSupplement.is_active == True)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supplement already in your active schedule"
        )
    
    user_supplement = UserSupplement(
        user_id=current_user.id,
        **supplement_data.dict()
    )
    
    db.add(user_supplement)
    db.commit()
    db.refresh(user_supplement)
    
    return user_supplement

@router.put("/my-supplements/{user_supplement_id}", response_model=UserSupplementResponse)
async def update_supplement_schedule(
    user_supplement_id: int,
    supplement_data: UserSupplementCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update user's supplement schedule"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()
    
    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )
    
    for field, value in supplement_data.dict(exclude_unset=True).items():
        setattr(user_supplement, field, value)
    
    db.commit()
    db.refresh(user_supplement)
    
    return user_supplement

@router.delete("/my-supplements/{user_supplement_id}")
async def remove_supplement_from_schedule(
    user_supplement_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Remove supplement from user's schedule (soft delete)"""
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()
    
    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )
    
    user_supplement.is_active = False
    db.commit()
    
    return {"message": "Supplement removed from schedule"}

# Intake tracking
@router.post("/intake", response_model=IntakeLogResponse, status_code=status.HTTP_201_CREATED)
async def log_supplement_intake(
    intake_data: IntakeLogCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Log supplement intake"""
    # Verify user supplement exists and belongs to current user
    user_supplement = db.query(UserSupplement).filter(
        (UserSupplement.id == intake_data.user_supplement_id) &
        (UserSupplement.user_id == current_user.id)
    ).first()

    if not user_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement schedule not found"
        )

    intake_log = IntakeLog(
        user_id=current_user.id,
        **intake_data.dict()
    )

    db.add(intake_log)
    db.commit()
    db.refresh(intake_log)

    return intake_log

@router.get("/intake/history", response_model=List[IntakeLogResponse])
async def get_intake_history(
    skip: int = 0,
    limit: int = 100,
    supplement_id: Optional[int] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's supplement intake history"""
    query = db.query(IntakeLog).filter(IntakeLog.user_id == current_user.id)

    if supplement_id:
        query = query.join(UserSupplement).filter(UserSupplement.supplement_id == supplement_id)

    if start_date:
        query = query.filter(IntakeLog.taken_at >= start_date)

    if end_date:
        query = query.filter(IntakeLog.taken_at <= end_date)

    intake_logs = query.order_by(IntakeLog.taken_at.desc()).offset(skip).limit(limit).all()
    return intake_logs

@router.get("/intake/today")
async def get_today_intake(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get today's intake logs"""
    today = date.today()
    intake_logs = db.query(IntakeLog).filter(
        (IntakeLog.user_id == current_user.id) &
        (IntakeLog.taken_at >= today)
    ).all()

    return {
        "date": today.isoformat(),
        "total_intakes": len(intake_logs),
        "intakes": intake_logs
    }

@router.get("/intake/streak")
async def get_intake_streak(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Calculate user's current intake streak"""
    from datetime import timedelta

    # Get all user supplements
    user_supplements = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.is_active == True)
    ).all()

    if not user_supplements:
        return {"current_streak": 0, "longest_streak": 0}

    # Calculate streak by checking consecutive days with all supplements taken
    current_date = date.today()
    current_streak = 0
    longest_streak = 0
    temp_streak = 0

    # Check last 365 days
    for i in range(365):
        check_date = current_date - timedelta(days=i)

        # Count intakes for this date
        daily_intakes = db.query(IntakeLog).filter(
            (IntakeLog.user_id == current_user.id) &
            (IntakeLog.taken_at >= check_date) &
            (IntakeLog.taken_at < check_date + timedelta(days=1))
        ).count()

        # Check if all supplements were taken (simplified - could be more sophisticated)
        if daily_intakes >= len(user_supplements):
            temp_streak += 1
            if i == 0:  # Today
                current_streak = temp_streak
        else:
            if temp_streak > longest_streak:
                longest_streak = temp_streak
            temp_streak = 0
            if i == 0:  # Today
                current_streak = 0

    if temp_streak > longest_streak:
        longest_streak = temp_streak

    return {
        "current_streak": current_streak,
        "longest_streak": longest_streak,
        "total_supplements": len(user_supplements)
    }

@router.get("/schedule/upcoming")
async def get_upcoming_schedule(
    hours_ahead: int = 24,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get upcoming supplement schedule"""
    from datetime import datetime, timedelta

    now = datetime.utcnow()
    end_time = now + timedelta(hours=hours_ahead)

    # Get active user supplements
    user_supplements = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.is_active == True)
    ).all()

    upcoming = []
    for user_supplement in user_supplements:
        # Simple scheduling logic - can be enhanced
        if user_supplement.time_of_day:
            # Calculate next scheduled time based on time_of_day
            time_mapping = {
                'morning': 8,
                'afternoon': 14,
                'evening': 18,
                'night': 22
            }

            hour = time_mapping.get(user_supplement.time_of_day.lower(), 12)

            # Check if already taken today
            today = date.today()
            taken_today = db.query(IntakeLog).filter(
                (IntakeLog.user_supplement_id == user_supplement.id) &
                (IntakeLog.taken_at >= today)
            ).first()

            if not taken_today:
                scheduled_time = datetime.combine(today, datetime.min.time().replace(hour=hour))
                if scheduled_time <= end_time:
                    upcoming.append({
                        "user_supplement_id": user_supplement.id,
                        "supplement_name": user_supplement.custom_name or user_supplement.supplement.name,
                        "dosage": user_supplement.dosage,
                        "scheduled_time": scheduled_time.isoformat(),
                        "time_of_day": user_supplement.time_of_day,
                        "overdue": scheduled_time < now
                    })

    return {
        "upcoming_count": len(upcoming),
        "schedule": sorted(upcoming, key=lambda x: x["scheduled_time"])
    }

@router.get("/analytics/adherence")
async def get_adherence_analytics(
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get detailed adherence analytics"""
    from datetime import datetime, timedelta

    end_date = date.today()
    start_date = end_date - timedelta(days=days)

    # Get active supplements during this period
    user_supplements = db.query(UserSupplement).filter(
        (UserSupplement.user_id == current_user.id) &
        (UserSupplement.is_active == True) &
        (UserSupplement.start_date <= end_date)
    ).all()

    if not user_supplements:
        return {
            "period_days": days,
            "total_supplements": 0,
            "adherence_rate": 0,
            "daily_breakdown": [],
            "supplement_breakdown": []
        }

    # Calculate daily adherence
    daily_breakdown = []
    total_expected = 0
    total_taken = 0

    for i in range(days):
        check_date = start_date + timedelta(days=i)

        # Count expected supplements for this day
        expected_count = len([s for s in user_supplements if s.start_date <= check_date])

        # Count actual intakes for this day
        actual_count = db.query(IntakeLog).filter(
            (IntakeLog.user_id == current_user.id) &
            (IntakeLog.taken_at >= check_date) &
            (IntakeLog.taken_at < check_date + timedelta(days=1))
        ).count()

        adherence = (actual_count / expected_count * 100) if expected_count > 0 else 0

        daily_breakdown.append({
            "date": check_date.isoformat(),
            "expected": expected_count,
            "taken": actual_count,
            "adherence_rate": round(adherence, 1)
        })

        total_expected += expected_count
        total_taken += actual_count

    # Calculate per-supplement adherence
    supplement_breakdown = []
    for user_supplement in user_supplements:
        supplement_intakes = db.query(IntakeLog).filter(
            (IntakeLog.user_supplement_id == user_supplement.id) &
            (IntakeLog.taken_at >= start_date) &
            (IntakeLog.taken_at <= end_date)
        ).count()

        # Calculate expected intakes (simplified - daily frequency assumed)
        supplement_days = min(days, (end_date - user_supplement.start_date).days + 1)
        expected_intakes = supplement_days

        adherence = (supplement_intakes / expected_intakes * 100) if expected_intakes > 0 else 0

        supplement_breakdown.append({
            "supplement_id": user_supplement.supplement_id,
            "supplement_name": user_supplement.custom_name or user_supplement.supplement.name,
            "expected": expected_intakes,
            "taken": supplement_intakes,
            "adherence_rate": round(adherence, 1)
        })

    overall_adherence = (total_taken / total_expected * 100) if total_expected > 0 else 0

    return {
        "period_days": days,
        "total_supplements": len(user_supplements),
        "adherence_rate": round(overall_adherence, 1),
        "total_expected": total_expected,
        "total_taken": total_taken,
        "daily_breakdown": daily_breakdown,
        "supplement_breakdown": supplement_breakdown
    }

@router.get("/analytics/mood-correlation")
async def get_mood_correlation(
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Analyze mood correlation with supplement intake"""
    from datetime import timedelta

    end_date = date.today()
    start_date = end_date - timedelta(days=days)

    # Get intake logs with mood data
    intake_logs = db.query(IntakeLog).filter(
        (IntakeLog.user_id == current_user.id) &
        (IntakeLog.taken_at >= start_date) &
        (IntakeLog.taken_at <= end_date) &
        (IntakeLog.mood_before.isnot(None) | IntakeLog.mood_after.isnot(None))
    ).all()

    if not intake_logs:
        return {
            "period_days": days,
            "total_mood_entries": 0,
            "average_mood_before": None,
            "average_mood_after": None,
            "mood_improvement": None,
            "daily_mood_trend": []
        }

    # Calculate mood statistics
    mood_before_values = [log.mood_before for log in intake_logs if log.mood_before is not None]
    mood_after_values = [log.mood_after for log in intake_logs if log.mood_after is not None]

    avg_mood_before = sum(mood_before_values) / len(mood_before_values) if mood_before_values else None
    avg_mood_after = sum(mood_after_values) / len(mood_after_values) if mood_after_values else None

    mood_improvement = None
    if avg_mood_before and avg_mood_after:
        mood_improvement = avg_mood_after - avg_mood_before

    # Daily mood trend
    daily_mood = {}
    for log in intake_logs:
        day = log.taken_at.date().isoformat()
        if day not in daily_mood:
            daily_mood[day] = {"before": [], "after": []}

        if log.mood_before:
            daily_mood[day]["before"].append(log.mood_before)
        if log.mood_after:
            daily_mood[day]["after"].append(log.mood_after)

    daily_trend = []
    for day, moods in daily_mood.items():
        avg_before = sum(moods["before"]) / len(moods["before"]) if moods["before"] else None
        avg_after = sum(moods["after"]) / len(moods["after"]) if moods["after"] else None

        daily_trend.append({
            "date": day,
            "average_mood_before": round(avg_before, 1) if avg_before else None,
            "average_mood_after": round(avg_after, 1) if avg_after else None,
            "entries_count": len(moods["before"]) + len(moods["after"])
        })

    return {
        "period_days": days,
        "total_mood_entries": len(intake_logs),
        "average_mood_before": round(avg_mood_before, 1) if avg_mood_before else None,
        "average_mood_after": round(avg_mood_after, 1) if avg_mood_after else None,
        "mood_improvement": round(mood_improvement, 1) if mood_improvement else None,
        "daily_mood_trend": sorted(daily_trend, key=lambda x: x["date"])
    }

@router.post("/schedule/reminder")
async def set_reminder(
    reminder_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Set reminder for supplement intake (placeholder for future notification system)"""
    # This would integrate with a notification system
    # For now, just return success
    return {
        "message": "Reminder set successfully",
        "reminder_data": reminder_data,
        "note": "Notification system integration pending"
    }
