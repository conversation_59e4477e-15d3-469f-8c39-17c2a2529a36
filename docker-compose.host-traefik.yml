services:
  # Backend API (FastAPI with host Traefik integration)
  backend:
    image: python:3.11-slim
    container_name: supplement-backend
    working_dir: /app
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./backend:/app
    networks:
      - supplement-network
      - traefik-network
    command: >
      sh -c "
        pip install -r requirements.txt &&
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
      "
    labels:
      # Enable Traefik
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-network"
      
      # HTTP router for API
      - "traefik.http.routers.supplement-backend.rule=Host(`api.pills.localhost`)"
      - "traefik.http.routers.supplement-backend.entrypoints=web"
      - "traefik.http.routers.supplement-backend.middlewares=cors-headers"
      
      # Service configuration
      - "traefik.http.services.supplement-backend.loadbalancer.server.port=8000"
      
      # CORS middleware
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolalloworiginlist=http://app.pills.localhost,https://app.pills.localhost"
      - "traefik.http.middlewares.cors-headers.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.cors-headers.headers.addvaryheader=true"

  # Frontend Application (Nginx with host Traefik integration)
  frontend:
    image: nginx:alpine
    container_name: supplement-frontend
    volumes:
      - ./frontend/public:/usr/share/nginx/html
    networks:
      - supplement-network
      - traefik-network
    labels:
      # Enable Traefik
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-network"
      
      # HTTP router for frontend
      - "traefik.http.routers.supplement-frontend.rule=Host(`app.pills.localhost`)"
      - "traefik.http.routers.supplement-frontend.entrypoints=web"
      
      # Service configuration
      - "traefik.http.services.supplement-frontend.loadbalancer.server.port=80"

  # Local Traefik for development (optional - can be disabled when using host Traefik)
  traefik:
    image: traefik:v3.0
    container_name: supplement-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--log.level=INFO"
    ports:
      - "9080:80"    # Fallback HTTP port
      - "9081:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - supplement-network
      - traefik-network
    labels:
      # Enable Traefik for dashboard access
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-network"
      
      # Dashboard router
      - "traefik.http.routers.supplement-traefik-dashboard.rule=Host(`traefik.pills.localhost`)"
      - "traefik.http.routers.supplement-traefik-dashboard.entrypoints=web"
      - "traefik.http.services.supplement-traefik-dashboard.loadbalancer.server.port=8080"

networks:
  supplement-network:
    driver: bridge
  
  # External Traefik network (should already exist on host)
  traefik-network:
    external: true
