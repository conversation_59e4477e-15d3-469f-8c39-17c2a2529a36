{"name": "supplement-tracker-mvp", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.17.9", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^10.18.0", "lucide-react": "^0.312.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.21.1", "react-scripts": "5.0.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "tailwindcss": "^3.4.0", "typescript": "^4.9.5", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.11", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}