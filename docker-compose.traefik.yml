services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: supplement-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.myresolver.acme.tlschallenge=true"
      - "--certificatesresolvers.myresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.myresolver.acme.storage=/letsencrypt/acme.json"
      - "--log.level=INFO"
      - "--accesslog=true"
    ports:
      - "9080:80"
      - "9443:443"
      - "9081:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-letsencrypt:/letsencrypt
    networks:
      - supplement-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.pills.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: supplement-postgres
    environment:
      POSTGRES_DB: supplement_tracker
      POSTGRES_USER: supplement_user
      POSTGRES_PASSWORD: supplement_pass
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - supplement-network
    labels:
      - "traefik.enable=false"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U supplement_user -d supplement_tracker"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: supplement-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - supplement-network
    labels:
      - "traefik.enable=false"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: supplement-backend
    environment:
      - DATABASE_URL=**********************************************************/supplement_tracker
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - DEBUG=False
      - ALLOWED_HOSTS=api.pills.localhost,localhost,127.0.0.1
      - CORS_ALLOWED_ORIGINS=https://app.pills.localhost,http://app.pills.localhost,http://localhost:3000
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app
      - backend_media:/app/media
      - backend_static:/app/staticfiles
    networks:
      - supplement-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.supplement-backend.rule=Host(`api.pills.localhost`)"
      - "traefik.http.routers.supplement-backend.entrypoints=web"
      - "traefik.http.services.supplement-backend.loadbalancer.server.port=8000"
      - "traefik.http.routers.supplement-backend.middlewares=supplement-cors-headers"
      - "traefik.http.middlewares.supplement-cors-headers.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.supplement-cors-headers.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.supplement-cors-headers.headers.accesscontrolalloworiginlist=https://app.pills.localhost,http://app.pills.localhost"
      - "traefik.http.middlewares.supplement-cors-headers.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.supplement-cors-headers.headers.addvaryheader=true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=http://api.pills.localhost
        - REACT_APP_ENVIRONMENT=production
        - REACT_APP_VERSION=1.0.0
    container_name: supplement-frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - supplement-network
    depends_on:
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.supplement-frontend.rule=Host(`app.pills.localhost`)"
      - "traefik.http.routers.supplement-frontend.entrypoints=web"
      - "traefik.http.services.supplement-frontend.loadbalancer.server.port=80"
      - "traefik.http.routers.supplement-frontend.middlewares=supplement-frontend-headers"
      - "traefik.http.middlewares.supplement-frontend-headers.headers.customrequestheaders.X-Forwarded-Proto=http"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx for static files (optional)
  nginx:
    image: nginx:alpine
    container_name: supplement-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - backend_static:/var/www/static:ro
      - backend_media:/var/www/media:ro
    networks:
      - supplement-network
    depends_on:
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nginx.rule=Host(`static.pills.localhost`)"
      - "traefik.http.routers.nginx.entrypoints=web"
      - "traefik.http.services.nginx.loadbalancer.server.port=80"

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: supplement-adminer
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    networks:
      - supplement-network
    depends_on:
      - postgres
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.adminer.rule=Host(`db.pills.localhost`)"
      - "traefik.http.routers.adminer.entrypoints=web"
      - "traefik.http.services.adminer.loadbalancer.server.port=8080"

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: supplement-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    networks:
      - supplement-network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.redis-commander.rule=Host(`redis.pills.localhost`)"
      - "traefik.http.routers.redis-commander.entrypoints=web"
      - "traefik.http.services.redis-commander.loadbalancer.server.port=8081"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_media:
    driver: local
  backend_static:
    driver: local
  traefik-letsencrypt:
    driver: local

networks:
  supplement-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
