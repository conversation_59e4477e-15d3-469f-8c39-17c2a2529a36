# 🔧 **Troubleshooting Portless URLs**

## 🚨 **Issue: 404 Page Not Found for pills.localhost**

If you're seeing a "404 page not found" error when accessing `http://app.pills.localhost`, here are the troubleshooting steps:

## 🔍 **Diagnosis Steps**

### **1. Verify Services Are Running**
```bash
# Check service status
./supplement-tracker status

# Expected output should show all services as "Running & Registered"
```

### **2. Test with curl (Command Line)**
```bash
# Test frontend
curl -s http://app.pills.localhost | head -3

# Test backend
curl -s http://api.pills.localhost/health/

# If these work, the issue is browser-specific
```

### **3. Check DNS Resolution**
```bash
# Check if domains resolve
./scripts/setup-hosts.sh check

# Should show domains resolve correctly
```

## 🛠️ **Solutions**

### **Solution 1: Browser DNS Cache**
The most common issue is browser DNS cache. Try these steps:

**Chrome/Chromium:**
1. Open `chrome://net-internals/#dns`
2. Click "Clear host cache"
3. Refresh the page

**Firefox:**
1. Type `about:networking#dns` in address bar
2. Click "Clear DNS Cache"
3. Refresh the page

**All Browsers:**
- Try **Incognito/Private mode**
- Try **Hard refresh** (Ctrl+F5 or Cmd+Shift+R)

### **Solution 2: Add Manual /etc/hosts Entries**
If DNS resolution isn't working automatically:

```bash
# Add hosts entries (requires sudo)
sudo ./scripts/setup-hosts.sh add

# Verify entries were added
./scripts/setup-hosts.sh check
```

### **Solution 3: Use Alternative URLs**
If portless URLs don't work, use the fallback URLs:

- **Frontend**: http://localhost:9080
- **Backend**: http://localhost:9080 (with Host header routing)
- **Traefik Dashboard**: http://localhost:9081

### **Solution 4: System DNS Configuration**

**For systemd-resolved systems:**
```bash
# Check if systemd-resolved is handling .localhost
systemctl status systemd-resolved

# Restart DNS resolver
sudo systemctl restart systemd-resolved
```

**For other DNS systems:**
```bash
# Check current DNS configuration
cat /etc/resolv.conf

# Flush DNS cache (varies by system)
sudo systemctl flush-dns  # systemd
sudo dscacheutil -flushcache  # macOS
```

## 🔧 **Advanced Troubleshooting**

### **Check Traefik Configuration**
```bash
# View Traefik dashboard
open http://localhost:8080

# Check if services are registered in Traefik
# Look for "supplement-frontend", "supplement-backend" in the dashboard
```

### **Check Container Networks**
```bash
# Verify containers are on Traefik network
docker network inspect traefik-network --format '{{range .Containers}}{{.Name}} {{end}}'

# Should show: supplement-backend traefik-controller supplement-traefik supplement-frontend
```

### **Check Container Labels**
```bash
# Verify Traefik labels on frontend
docker inspect supplement-frontend --format '{{range $key, $value := .Config.Labels}}{{$key}}={{$value}}{{"\n"}}{{end}}' | grep traefik

# Should show proper routing rules
```

### **Manual Host Header Test**
```bash
# Test with explicit Host header
curl -H "Host: app.pills.localhost" http://localhost/

# If this works but pills.localhost doesn't, it's a DNS issue
```

## 🎯 **Quick Fixes**

### **Option A: Use Localhost with Port**
If you need immediate access:
```bash
# Stop current setup
./supplement-tracker stop

# Start with original port-based setup
docker compose -f docker-compose.simple.yml up -d

# Access via:
# - Frontend: http://localhost:9080
# - Backend: http://localhost:9080 (routed by internal Traefik)
```

### **Option B: Force DNS Resolution**
Add to `/etc/hosts` manually:
```bash
echo "127.0.0.1 app.pills.localhost api.pills.localhost traefik.pills.localhost" | sudo tee -a /etc/hosts
```

### **Option C: Use IP Address**
Access directly via IP:
```bash
# Find container IP
docker inspect supplement-frontend --format '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}'

# Access via IP (example)
# http://**********
```

## 🔍 **Debugging Commands**

### **Network Debugging**
```bash
# Check all networks
docker network ls

# Inspect Traefik network
docker network inspect traefik-network

# Check container connectivity
docker exec supplement-frontend ping traefik-controller
```

### **DNS Debugging**
```bash
# Test DNS resolution
nslookup app.pills.localhost
dig app.pills.localhost

# Check system DNS
cat /etc/resolv.conf
systemctl status systemd-resolved
```

### **Traefik Debugging**
```bash
# Check Traefik logs
docker logs traefik-controller

# Check our Traefik logs
docker logs supplement-traefik

# View Traefik configuration
curl http://localhost:8080/api/rawdata
```

## 🎯 **Expected Working State**

When everything is working correctly:

### **Service Status**
```bash
./supplement-tracker status
# All services should show "Running & Registered"
```

### **URL Access**
- ✅ http://app.pills.localhost - Loads frontend
- ✅ http://api.pills.localhost/docs - Shows API documentation
- ✅ http://traefik.pills.localhost - Shows Traefik dashboard

### **curl Tests**
```bash
curl -s http://app.pills.localhost | grep -q "DOCTYPE html"  # Should succeed
curl -s http://api.pills.localhost/health/ | grep -q "healthy"  # Should succeed
```

## 🆘 **If All Else Fails**

### **Reset and Restart**
```bash
# Complete reset
./supplement-tracker stop
docker system prune -f
./supplement-tracker start

# Re-register services
./supplement-tracker register
```

### **Use Fallback Configuration**
```bash
# Switch back to port-based access
docker compose -f docker-compose.simple.yml up -d

# Access via:
# - http://localhost:9080 (frontend)
# - http://localhost:9081 (Traefik dashboard)
```

### **Contact Support**
If issues persist, provide this information:
- Operating system and version
- Browser and version
- Output of `./supplement-tracker status`
- Output of `docker network ls`
- Output of `curl -v http://app.pills.localhost`

## 📝 **Common Issues Summary**

| Issue | Cause | Solution |
|-------|-------|----------|
| 404 in browser | Browser DNS cache | Clear DNS cache, try incognito |
| Works in curl, not browser | Browser-specific DNS | Add /etc/hosts entries |
| No DNS resolution | Missing DNS config | Use setup-hosts.sh script |
| Traefik not routing | Network disconnection | Re-run registration script |
| Services not starting | Port conflicts | Check for conflicting services |

The portless setup should work seamlessly once DNS resolution is properly configured! 🚀
