#!/bin/bash

# Setup /etc/hosts entries for Supplement Tracker *.pills.localhost domains
# This script adds the necessary entries to access the services via domain names

echo "🔧 Setting up /etc/hosts entries for Supplement Tracker..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script needs to be run as root (use sudo)"
    echo "Usage: sudo ./setup-hosts.sh"
    exit 1
fi

# Backup original hosts file
cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)
echo "✅ Backed up original /etc/hosts file"

# Define the entries to add
ENTRIES=(
    "127.0.0.1 app.pills.localhost"
    "127.0.0.1 api.pills.localhost"
    "127.0.0.1 traefik.pills.localhost"
)

# Add entries if they don't already exist
echo ""
echo "📝 Adding entries to /etc/hosts:"

for entry in "${ENTRIES[@]}"; do
    domain=$(echo "$entry" | awk '{print $2}')
    
    if grep -q "$domain" /etc/hosts; then
        echo "   ⚠️  $domain already exists in /etc/hosts"
    else
        echo "$entry" >> /etc/hosts
        echo "   ✅ Added: $entry"
    fi
done

echo ""
echo "🎉 Setup complete! You can now access:"
echo "   • Frontend (HTTPS):  https://app.pills.localhost/"
echo "   • Frontend (HTTP):   http://app.pills.localhost:9080/"
echo "   • API (HTTPS):       https://api.pills.localhost/"
echo "   • API (HTTP):        http://api.pills.localhost:9080/"
echo "   • Dashboard:         http://traefik.pills.localhost:9081/"
echo ""
echo "📋 To verify, run:"
echo "   curl https://api.pills.localhost/health/ -k"
echo "   curl http://api.pills.localhost:9080/health/"
echo ""
echo "🔄 To remove these entries later, restore from backup:"
echo "   sudo cp /etc/hosts.backup.* /etc/hosts"
